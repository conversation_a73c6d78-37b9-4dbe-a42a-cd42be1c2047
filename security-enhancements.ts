// 安全性增强方案

import * as crypto from 'crypto';
import * as jwt from 'jsonwebtoken';
import { Context } from 'koa';

// 1. 增强的身份验证系统
export class EnhancedAuthSystem {
  private readonly jwtSecret: string;
  private readonly refreshTokens: Map<string, RefreshTokenData> = new Map();
  private readonly loginAttempts: Map<string, LoginAttemptData> = new Map();
  private readonly maxLoginAttempts = 5;
  private readonly lockoutDuration = 15 * 60 * 1000; // 15分钟

  constructor(jwtSecret: string) {
    this.jwtSecret = jwtSecret;
  }

  // 增强的用户注册
  async registerUser(userData: UserRegistrationData): Promise<AuthResult> {
    // 验证输入数据
    const validation = this.validateUserData(userData);
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    // 检查用户名是否已存在
    if (await this.userExists(userData.username)) {
      return { success: false, error: '用户名已存在' };
    }

    // 密码强度检查
    const passwordStrength = this.checkPasswordStrength(userData.password);
    if (!passwordStrength.valid) {
      return { success: false, error: passwordStrength.error };
    }

    // 生成盐值和哈希密码
    const salt = crypto.randomBytes(32).toString('hex');
    const hashedPassword = await this.hashPassword(userData.password, salt);

    // 创建用户
    const user: User = {
      id: this.generateUserId(),
      username: userData.username,
      passwordHash: hashedPassword,
      salt,
      email: userData.email,
      createdAt: Date.now(),
      lastLogin: 0,
      isActive: true,
      roles: ['player'],
      securitySettings: {
        twoFactorEnabled: false,
        loginNotifications: true,
        sessionTimeout: 24 * 60 * 60 * 1000 // 24小时
      }
    };

    // 保存用户（这里需要实际的数据库操作）
    await this.saveUser(user);

    // 生成令牌
    const tokens = this.generateTokens(user);

    return {
      success: true,
      user: this.sanitizeUser(user),
      tokens
    };
  }

  // 增强的用户登录
  async loginUser(username: string, password: string, ip: string): Promise<AuthResult> {
    // 检查登录尝试次数
    if (this.isAccountLocked(ip)) {
      return { 
        success: false, 
        error: '账户已被锁定，请稍后再试',
        lockoutTime: this.getLockoutTime(ip)
      };
    }

    // 获取用户
    const user = await this.getUserByUsername(username);
    if (!user) {
      this.recordFailedLogin(ip);
      return { success: false, error: '用户名或密码错误' };
    }

    // 验证密码
    const isValidPassword = await this.verifyPassword(password, user.passwordHash, user.salt);
    if (!isValidPassword) {
      this.recordFailedLogin(ip);
      return { success: false, error: '用户名或密码错误' };
    }

    // 检查账户状态
    if (!user.isActive) {
      return { success: false, error: '账户已被禁用' };
    }

    // 清除失败登录记录
    this.clearFailedLogins(ip);

    // 更新最后登录时间
    user.lastLogin = Date.now();
    await this.updateUser(user);

    // 生成令牌
    const tokens = this.generateTokens(user);

    return {
      success: true,
      user: this.sanitizeUser(user),
      tokens
    };
  }

  // 令牌刷新
  async refreshToken(refreshToken: string): Promise<AuthResult> {
    const tokenData = this.refreshTokens.get(refreshToken);
    if (!tokenData || tokenData.expiresAt < Date.now()) {
      return { success: false, error: '刷新令牌无效或已过期' };
    }

    const user = await this.getUserById(tokenData.userId);
    if (!user || !user.isActive) {
      return { success: false, error: '用户不存在或已被禁用' };
    }

    // 生成新令牌
    const tokens = this.generateTokens(user);
    
    // 删除旧的刷新令牌
    this.refreshTokens.delete(refreshToken);

    return {
      success: true,
      user: this.sanitizeUser(user),
      tokens
    };
  }

  // 生成令牌
  private generateTokens(user: User): TokenPair {
    const accessToken = jwt.sign(
      { 
        userId: user.id, 
        username: user.username,
        roles: user.roles 
      },
      this.jwtSecret,
      { expiresIn: '1h' }
    );

    const refreshToken = crypto.randomBytes(64).toString('hex');
    
    // 存储刷新令牌
    this.refreshTokens.set(refreshToken, {
      userId: user.id,
      expiresAt: Date.now() + 7 * 24 * 60 * 60 * 1000 // 7天
    });

    return { accessToken, refreshToken };
  }

  // 验证访问令牌
  verifyAccessToken(token: string): TokenPayload | null {
    try {
      return jwt.verify(token, this.jwtSecret) as TokenPayload;
    } catch {
      return null;
    }
  }

  // 密码强度检查
  private checkPasswordStrength(password: string): ValidationResult {
    if (password.length < 8) {
      return { valid: false, error: '密码长度至少8位' };
    }

    if (!/[A-Z]/.test(password)) {
      return { valid: false, error: '密码必须包含大写字母' };
    }

    if (!/[a-z]/.test(password)) {
      return { valid: false, error: '密码必须包含小写字母' };
    }

    if (!/[0-9]/.test(password)) {
      return { valid: false, error: '密码必须包含数字' };
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return { valid: false, error: '密码必须包含特殊字符' };
    }

    return { valid: true };
  }

  // 哈希密码
  private async hashPassword(password: string, salt: string): Promise<string> {
    return new Promise((resolve, reject) => {
      crypto.pbkdf2(password, salt, 100000, 64, 'sha512', (err, derivedKey) => {
        if (err) reject(err);
        else resolve(derivedKey.toString('hex'));
      });
    });
  }

  // 验证密码
  private async verifyPassword(password: string, hash: string, salt: string): Promise<boolean> {
    const hashedPassword = await this.hashPassword(password, salt);
    return crypto.timingSafeEqual(Buffer.from(hash, 'hex'), Buffer.from(hashedPassword, 'hex'));
  }

  // 其他辅助方法...
  private validateUserData(data: UserRegistrationData): ValidationResult {
    if (!data.username || data.username.length < 3) {
      return { valid: false, error: '用户名长度至少3位' };
    }

    if (!/^[a-zA-Z0-9_]+$/.test(data.username)) {
      return { valid: false, error: '用户名只能包含字母、数字和下划线' };
    }

    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      return { valid: false, error: '邮箱格式不正确' };
    }

    return { valid: true };
  }

  private isAccountLocked(ip: string): boolean {
    const attempts = this.loginAttempts.get(ip);
    if (!attempts) return false;

    return attempts.count >= this.maxLoginAttempts && 
           Date.now() - attempts.lastAttempt < this.lockoutDuration;
  }

  private recordFailedLogin(ip: string): void {
    const attempts = this.loginAttempts.get(ip) || { count: 0, lastAttempt: 0 };
    attempts.count++;
    attempts.lastAttempt = Date.now();
    this.loginAttempts.set(ip, attempts);
  }

  private clearFailedLogins(ip: string): void {
    this.loginAttempts.delete(ip);
  }

  private getLockoutTime(ip: string): number {
    const attempts = this.loginAttempts.get(ip);
    return attempts ? attempts.lastAttempt + this.lockoutDuration : 0;
  }

  private generateUserId(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  private sanitizeUser(user: User): SafeUser {
    return {
      id: user.id,
      username: user.username,
      email: user.email,
      createdAt: user.createdAt,
      lastLogin: user.lastLogin,
      roles: user.roles
    };
  }

  // 这些方法需要实际的数据库实现
  private async userExists(username: string): Promise<boolean> {
    // 实现数据库查询
    return false;
  }

  private async saveUser(user: User): Promise<void> {
    // 实现数据库保存
  }

  private async getUserByUsername(username: string): Promise<User | null> {
    // 实现数据库查询
    return null;
  }

  private async getUserById(id: string): Promise<User | null> {
    // 实现数据库查询
    return null;
  }

  private async updateUser(user: User): Promise<void> {
    // 实现数据库更新
  }
}

// 2. 游戏安全监控
export class GameSecurityMonitor {
  private suspiciousActivities: Map<string, SuspiciousActivity[]> = new Map();
  private bannedIPs: Set<string> = new Set();
  private rateLimiters: Map<string, RateLimiter> = new Map();

  // 检测可疑活动
  detectSuspiciousActivity(playerId: string, activity: GameActivity): SecurityAlert | null {
    if (!this.suspiciousActivities.has(playerId)) {
      this.suspiciousActivities.set(playerId, []);
    }

    const playerActivities = this.suspiciousActivities.get(playerId)!;
    
    // 检测异常快速操作
    if (this.detectRapidActions(playerActivities, activity)) {
      return {
        type: 'rapid_actions',
        playerId,
        severity: 'medium',
        description: '检测到异常快速的游戏操作',
        timestamp: Date.now()
      };
    }

    // 检测异常下注模式
    if (this.detectAbnormalBetting(playerActivities, activity)) {
      return {
        type: 'abnormal_betting',
        playerId,
        severity: 'high',
        description: '检测到异常的下注模式',
        timestamp: Date.now()
      };
    }

    // 检测多账户操作
    if (this.detectMultiAccounting(playerId, activity)) {
      return {
        type: 'multi_accounting',
        playerId,
        severity: 'critical',
        description: '疑似多账户操作',
        timestamp: Date.now()
      };
    }

    // 记录活动
    playerActivities.push({
      type: activity.type,
      timestamp: Date.now(),
      data: activity.data
    });

    // 保持最近100个活动记录
    if (playerActivities.length > 100) {
      playerActivities.shift();
    }

    return null;
  }

  // 检测快速操作
  private detectRapidActions(activities: SuspiciousActivity[], newActivity: GameActivity): boolean {
    const recentActivities = activities.filter(a => 
      Date.now() - a.timestamp < 10000 // 最近10秒
    );

    // 如果10秒内操作超过20次，认为可疑
    return recentActivities.length > 20;
  }

  // 检测异常下注
  private detectAbnormalBetting(activities: SuspiciousActivity[], newActivity: GameActivity): boolean {
    if (newActivity.type !== 'bet') return false;

    const betActivities = activities
      .filter(a => a.type === 'bet' && Date.now() - a.timestamp < 300000) // 最近5分钟
      .map(a => a.data.amount as number);

    if (betActivities.length < 5) return false;

    // 检查下注金额的标准差
    const mean = betActivities.reduce((sum, amount) => sum + amount, 0) / betActivities.length;
    const variance = betActivities.reduce((sum, amount) => sum + Math.pow(amount - mean, 2), 0) / betActivities.length;
    const stdDev = Math.sqrt(variance);

    // 如果标准差过大，可能是异常下注
    return stdDev > mean * 2;
  }

  // 检测多账户操作
  private detectMultiAccounting(playerId: string, activity: GameActivity): boolean {
    // 这里需要更复杂的逻辑，比如检查IP地址、设备指纹等
    // 简化实现
    return false;
  }

  // IP封禁管理
  banIP(ip: string, reason: string): void {
    this.bannedIPs.add(ip);
    console.log(`IP ${ip} 已被封禁: ${reason}`);
  }

  unbanIP(ip: string): void {
    this.bannedIPs.delete(ip);
    console.log(`IP ${ip} 已解封`);
  }

  isIPBanned(ip: string): boolean {
    return this.bannedIPs.has(ip);
  }

  // 速率限制
  checkRateLimit(identifier: string, limit: number, windowMs: number): boolean {
    if (!this.rateLimiters.has(identifier)) {
      this.rateLimiters.set(identifier, {
        count: 0,
        resetTime: Date.now() + windowMs
      });
    }

    const limiter = this.rateLimiters.get(identifier)!;
    
    if (Date.now() > limiter.resetTime) {
      limiter.count = 0;
      limiter.resetTime = Date.now() + windowMs;
    }

    limiter.count++;
    return limiter.count <= limit;
  }
}

// 3. 数据加密工具
export class DataEncryption {
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyLength = 32;
  private readonly ivLength = 16;
  private readonly tagLength = 16;

  // 生成加密密钥
  generateKey(): string {
    return crypto.randomBytes(this.keyLength).toString('hex');
  }

  // 加密数据
  encrypt(data: string, key: string): EncryptedData {
    const keyBuffer = Buffer.from(key, 'hex');
    const iv = crypto.randomBytes(this.ivLength);
    
    const cipher = crypto.createCipher(this.algorithm, keyBuffer);
    cipher.setAAD(Buffer.from('poker-game', 'utf8'));
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex')
    };
  }

  // 解密数据
  decrypt(encryptedData: EncryptedData, key: string): string {
    const keyBuffer = Buffer.from(key, 'hex');
    const iv = Buffer.from(encryptedData.iv, 'hex');
    const tag = Buffer.from(encryptedData.tag, 'hex');
    
    const decipher = crypto.createDecipher(this.algorithm, keyBuffer);
    decipher.setAAD(Buffer.from('poker-game', 'utf8'));
    decipher.setAuthTag(tag);
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  // 哈希数据
  hash(data: string, salt?: string): string {
    const actualSalt = salt || crypto.randomBytes(16).toString('hex');
    const hash = crypto.createHash('sha256');
    hash.update(data + actualSalt);
    return hash.digest('hex');
  }

  // 生成安全随机数
  generateSecureRandom(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }
}

// 4. 安全中间件
export const securityMiddleware = {
  // CSRF保护
  csrfProtection: async (ctx: Context, next: () => Promise<any>) => {
    if (['POST', 'PUT', 'DELETE'].includes(ctx.method)) {
      const token = ctx.headers['x-csrf-token'] || ctx.request.body?.csrfToken;
      const sessionToken = ctx.session?.csrfToken;
      
      if (!token || !sessionToken || token !== sessionToken) {
        ctx.status = 403;
        ctx.body = { error: 'CSRF token mismatch' };
        return;
      }
    }
    
    await next();
  },

  // XSS保护
  xssProtection: async (ctx: Context, next: () => Promise<any>) => {
    ctx.set('X-XSS-Protection', '1; mode=block');
    ctx.set('X-Content-Type-Options', 'nosniff');
    ctx.set('X-Frame-Options', 'DENY');
    
    await next();
  },

  // 内容安全策略
  contentSecurityPolicy: async (ctx: Context, next: () => Promise<any>) => {
    const csp = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "connect-src 'self' ws: wss:",
      "font-src 'self'",
      "object-src 'none'",
      "media-src 'self'",
      "frame-src 'none'"
    ].join('; ');
    
    ctx.set('Content-Security-Policy', csp);
    await next();
  }
};

// 类型定义
interface User {
  id: string;
  username: string;
  passwordHash: string;
  salt: string;
  email?: string;
  createdAt: number;
  lastLogin: number;
  isActive: boolean;
  roles: string[];
  securitySettings: {
    twoFactorEnabled: boolean;
    loginNotifications: boolean;
    sessionTimeout: number;
  };
}

interface SafeUser {
  id: string;
  username: string;
  email?: string;
  createdAt: number;
  lastLogin: number;
  roles: string[];
}

interface UserRegistrationData {
  username: string;
  password: string;
  email?: string;
}

interface AuthResult {
  success: boolean;
  user?: SafeUser;
  tokens?: TokenPair;
  error?: string;
  lockoutTime?: number;
}

interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

interface TokenPayload {
  userId: string;
  username: string;
  roles: string[];
  iat: number;
  exp: number;
}

interface RefreshTokenData {
  userId: string;
  expiresAt: number;
}

interface LoginAttemptData {
  count: number;
  lastAttempt: number;
}

interface ValidationResult {
  valid: boolean;
  error?: string;
}

interface GameActivity {
  type: string;
  data: any;
}

interface SuspiciousActivity {
  type: string;
  timestamp: number;
  data: any;
}

interface SecurityAlert {
  type: string;
  playerId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  timestamp: number;
}

interface RateLimiter {
  count: number;
  resetTime: number;
}

interface EncryptedData {
  encrypted: string;
  iv: string;
  tag: string;
}

export {
  EnhancedAuthSystem,
  GameSecurityMonitor,
  DataEncryption,
  securityMiddleware
};
