// 游戏体验增强功能

import React, { useState, useEffect, useRef } from 'react';
import { Button, Modal, Progress, notification, Tooltip, Badge } from 'antd';
import { SoundOutlined, SoundFilled, FullscreenOutlined, HistoryOutlined } from '@ant-design/icons';

// 1. 音效管理器
class SoundManager {
  private sounds: { [key: string]: HTMLAudioElement } = {};
  private enabled: boolean = true;
  private volume: number = 0.5;

  constructor() {
    this.loadSounds();
    this.enabled = localStorage.getItem('soundEnabled') !== 'false';
    this.volume = parseFloat(localStorage.getItem('soundVolume') || '0.5');
  }

  private loadSounds() {
    const soundFiles = {
      cardFlip: '/sounds/card-flip.mp3',
      chipDrop: '/sounds/chip-drop.mp3',
      buttonClick: '/sounds/button-click.mp3',
      gameStart: '/sounds/game-start.mp3',
      gameEnd: '/sounds/game-end.mp3',
      notification: '/sounds/notification.mp3',
      countdown: '/sounds/countdown.mp3',
      win: '/sounds/win.mp3',
      lose: '/sounds/lose.mp3'
    };

    Object.entries(soundFiles).forEach(([key, src]) => {
      const audio = new Audio(src);
      audio.volume = this.volume;
      audio.preload = 'auto';
      this.sounds[key] = audio;
    });
  }

  play(soundName: string) {
    if (!this.enabled || !this.sounds[soundName]) return;
    
    const sound = this.sounds[soundName];
    sound.currentTime = 0;
    sound.play().catch(e => console.log('Sound play failed:', e));
  }

  setEnabled(enabled: boolean) {
    this.enabled = enabled;
    localStorage.setItem('soundEnabled', enabled.toString());
  }

  setVolume(volume: number) {
    this.volume = volume;
    localStorage.setItem('soundVolume', volume.toString());
    Object.values(this.sounds).forEach(sound => {
      sound.volume = volume;
    });
  }

  isEnabled() {
    return this.enabled;
  }

  getVolume() {
    return this.volume;
  }
}

export const soundManager = new SoundManager();

// 2. 游戏统计面板
interface GameStats {
  handsPlayed: number;
  handsWon: number;
  totalWinnings: number;
  biggestPot: number;
  bestHand: string;
  playTime: number;
}

export const GameStatsPanel: React.FC<{ visible: boolean; onClose: () => void }> = ({ visible, onClose }) => {
  const [stats, setStats] = useState<GameStats>({
    handsPlayed: 0,
    handsWon: 0,
    totalWinnings: 0,
    biggestPot: 0,
    bestHand: '高牌',
    playTime: 0
  });

  useEffect(() => {
    // 从localStorage加载统计数据
    const savedStats = localStorage.getItem('gameStats');
    if (savedStats) {
      setStats(JSON.parse(savedStats));
    }
  }, [visible]);

  const winRate = stats.handsPlayed > 0 ? (stats.handsWon / stats.handsPlayed * 100).toFixed(1) : '0.0';
  const avgWinning = stats.handsWon > 0 ? (stats.totalWinnings / stats.handsWon).toFixed(0) : '0';

  return (
    <Modal
      title="游戏统计"
      visible={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>关闭</Button>
      ]}
      width={400}
    >
      <div style={{ padding: '16px 0' }}>
        <div style={{ marginBottom: '16px' }}>
          <h4>基础统计</h4>
          <p>总手数: <strong>{stats.handsPlayed}</strong></p>
          <p>获胜手数: <strong>{stats.handsWon}</strong></p>
          <p>胜率: <strong>{winRate}%</strong></p>
          <Progress percent={parseFloat(winRate)} strokeColor="#52c41a" />
        </div>
        
        <div style={{ marginBottom: '16px' }}>
          <h4>盈利统计</h4>
          <p>总盈利: <strong>{stats.totalWinnings > 0 ? '+' : ''}{stats.totalWinnings}</strong></p>
          <p>最大单锅: <strong>{stats.biggestPot}</strong></p>
          <p>平均获胜: <strong>{avgWinning}</strong></p>
        </div>
        
        <div>
          <h4>其他信息</h4>
          <p>最佳牌型: <strong>{stats.bestHand}</strong></p>
          <p>游戏时长: <strong>{Math.floor(stats.playTime / 60)}分钟</strong></p>
        </div>
      </div>
    </Modal>
  );
};

// 3. 快捷操作栏
export const QuickActionBar: React.FC = () => {
  const [soundEnabled, setSoundEnabled] = useState(soundManager.isEnabled());
  const [statsVisible, setStatsVisible] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const toggleSound = () => {
    const newState = !soundEnabled;
    setSoundEnabled(newState);
    soundManager.setEnabled(newState);
    soundManager.play('buttonClick');
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().then(() => {
        setIsFullscreen(true);
      });
    } else {
      document.exitFullscreen().then(() => {
        setIsFullscreen(false);
      });
    }
  };

  const showStats = () => {
    setStatsVisible(true);
    soundManager.play('buttonClick');
  };

  return (
    <>
      <div style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        zIndex: 1000,
        display: 'flex',
        gap: '8px',
        background: 'rgba(255, 255, 255, 0.9)',
        padding: '8px',
        borderRadius: '8px',
        backdropFilter: 'blur(10px)'
      }}>
        <Tooltip title={soundEnabled ? '关闭音效' : '开启音效'}>
          <Button
            type="text"
            icon={soundEnabled ? <SoundFilled /> : <SoundOutlined />}
            onClick={toggleSound}
            style={{ color: soundEnabled ? '#1890ff' : '#999' }}
          />
        </Tooltip>
        
        <Tooltip title="游戏统计">
          <Button
            type="text"
            icon={<HistoryOutlined />}
            onClick={showStats}
          />
        </Tooltip>
        
        <Tooltip title={isFullscreen ? '退出全屏' : '全屏显示'}>
          <Button
            type="text"
            icon={<FullscreenOutlined />}
            onClick={toggleFullscreen}
          />
        </Tooltip>
      </div>
      
      <GameStatsPanel 
        visible={statsVisible} 
        onClose={() => setStatsVisible(false)} 
      />
    </>
  );
};

// 4. 智能提示系统
export const SmartHints: React.FC<{ gameState: any }> = ({ gameState }) => {
  const [hints, setHints] = useState<string[]>([]);
  
  useEffect(() => {
    if (!gameState) return;
    
    const newHints: string[] = [];
    
    // 根据游戏状态生成提示
    if (gameState.currentPlayer && gameState.pot) {
      const potOdds = gameState.callAmount / (gameState.pot + gameState.callAmount);
      if (potOdds < 0.25) {
        newHints.push('底池赔率很好，考虑跟注');
      }
    }
    
    if (gameState.position === 'button') {
      newHints.push('你在按钮位，有位置优势');
    }
    
    if (gameState.opponents && gameState.opponents.length <= 3) {
      newHints.push('人数较少，可以更激进地游戏');
    }
    
    setHints(newHints);
  }, [gameState]);

  if (hints.length === 0) return null;

  return (
    <div style={{
      position: 'fixed',
      bottom: '200px',
      left: '10px',
      maxWidth: '250px',
      background: 'rgba(24, 144, 255, 0.9)',
      color: 'white',
      padding: '12px',
      borderRadius: '8px',
      fontSize: '12px',
      zIndex: 999
    }}>
      <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>💡 智能提示</div>
      {hints.map((hint, index) => (
        <div key={index} style={{ marginBottom: '2px' }}>• {hint}</div>
      ))}
    </div>
  );
};

// 5. 游戏记录器
export class GameRecorder {
  private static instance: GameRecorder;
  private records: any[] = [];
  
  static getInstance() {
    if (!GameRecorder.instance) {
      GameRecorder.instance = new GameRecorder();
    }
    return GameRecorder.instance;
  }

  recordAction(action: string, details: any) {
    const record = {
      timestamp: Date.now(),
      action,
      details,
      gameId: details.gameId || 'unknown'
    };
    
    this.records.push(record);
    
    // 保持最近1000条记录
    if (this.records.length > 1000) {
      this.records = this.records.slice(-1000);
    }
    
    // 保存到localStorage
    localStorage.setItem('gameRecords', JSON.stringify(this.records));
  }

  getRecords(gameId?: string) {
    if (gameId) {
      return this.records.filter(r => r.details.gameId === gameId);
    }
    return this.records;
  }

  exportRecords() {
    const data = JSON.stringify(this.records, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `poker-records-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }
}

// 6. 性能监控
export const PerformanceMonitor: React.FC = () => {
  const [fps, setFps] = useState(60);
  const [memory, setMemory] = useState(0);
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());

  useEffect(() => {
    const updateStats = () => {
      const now = performance.now();
      frameCount.current++;
      
      if (now - lastTime.current >= 1000) {
        setFps(Math.round(frameCount.current * 1000 / (now - lastTime.current)));
        frameCount.current = 0;
        lastTime.current = now;
        
        // 内存使用情况（如果支持）
        if ('memory' in performance) {
          const memInfo = (performance as any).memory;
          setMemory(Math.round(memInfo.usedJSHeapSize / 1024 / 1024));
        }
      }
      
      requestAnimationFrame(updateStats);
    };
    
    requestAnimationFrame(updateStats);
  }, []);

  // 只在开发环境显示
  if (process.env.NODE_ENV !== 'development') return null;

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      left: '10px',
      background: 'rgba(0, 0, 0, 0.7)',
      color: 'white',
      padding: '8px',
      borderRadius: '4px',
      fontSize: '12px',
      fontFamily: 'monospace',
      zIndex: 9999
    }}>
      <div>FPS: {fps}</div>
      {memory > 0 && <div>内存: {memory}MB</div>}
    </div>
  );
};

// 7. 自动操作助手
export const AutoActionHelper: React.FC<{ onAction: (action: string) => void }> = ({ onAction }) => {
  const [autoFold, setAutoFold] = useState(false);
  const [autoCheck, setAutoCheck] = useState(false);
  
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement) return;
      
      switch (e.key.toLowerCase()) {
        case 'f':
          onAction('fold');
          soundManager.play('buttonClick');
          break;
        case 'c':
          onAction('check');
          soundManager.play('buttonClick');
          break;
        case 'r':
          onAction('raise');
          soundManager.play('buttonClick');
          break;
        case 'a':
          onAction('allin');
          soundManager.play('buttonClick');
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [onAction]);

  return (
    <div style={{
      position: 'fixed',
      bottom: '10px',
      left: '10px',
      background: 'rgba(255, 255, 255, 0.9)',
      padding: '8px',
      borderRadius: '8px',
      fontSize: '12px',
      zIndex: 999
    }}>
      <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>快捷键</div>
      <div>F - 弃牌 | C - 过牌 | R - 加注 | A - 全下</div>
    </div>
  );
};

export default {
  soundManager,
  GameStatsPanel,
  QuickActionBar,
  SmartHints,
  GameRecorder,
  PerformanceMonitor,
  AutoActionHelper
};
