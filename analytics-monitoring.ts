// 数据分析与监控系统

import { EventEmitter } from 'events';

// 1. 实时数据分析器
export class RealTimeAnalytics extends EventEmitter {
  private metrics: Map<string, MetricData> = new Map();
  private alerts: AlertRule[] = [];
  private dataBuffer: DataPoint[] = [];
  private bufferSize = 1000;

  // 记录指标
  recordMetric(name: string, value: number, tags?: Record<string, string>) {
    const timestamp = Date.now();
    const dataPoint: DataPoint = { name, value, timestamp, tags };
    
    // 添加到缓冲区
    this.dataBuffer.push(dataPoint);
    if (this.dataBuffer.length > this.bufferSize) {
      this.dataBuffer.shift();
    }

    // 更新指标统计
    this.updateMetricStats(name, value, timestamp);
    
    // 检查告警
    this.checkAlerts(name, value);
    
    // 触发事件
    this.emit('metric', dataPoint);
  }

  // 更新指标统计
  private updateMetricStats(name: string, value: number, timestamp: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, {
        name,
        count: 0,
        sum: 0,
        min: value,
        max: value,
        avg: 0,
        lastValue: value,
        lastUpdate: timestamp,
        history: []
      });
    }

    const metric = this.metrics.get(name)!;
    metric.count++;
    metric.sum += value;
    metric.min = Math.min(metric.min, value);
    metric.max = Math.max(metric.max, value);
    metric.avg = metric.sum / metric.count;
    metric.lastValue = value;
    metric.lastUpdate = timestamp;
    
    // 保持最近100个值的历史
    metric.history.push({ value, timestamp });
    if (metric.history.length > 100) {
      metric.history.shift();
    }
  }

  // 添加告警规则
  addAlert(rule: AlertRule) {
    this.alerts.push(rule);
  }

  // 检查告警
  private checkAlerts(metricName: string, value: number) {
    this.alerts
      .filter(alert => alert.metric === metricName)
      .forEach(alert => {
        let triggered = false;
        
        switch (alert.condition) {
          case 'greater_than':
            triggered = value > alert.threshold;
            break;
          case 'less_than':
            triggered = value < alert.threshold;
            break;
          case 'equals':
            triggered = value === alert.threshold;
            break;
        }
        
        if (triggered) {
          this.emit('alert', {
            rule: alert,
            value,
            timestamp: Date.now()
          });
        }
      });
  }

  // 获取指标数据
  getMetric(name: string): MetricData | null {
    return this.metrics.get(name) || null;
  }

  // 获取所有指标
  getAllMetrics(): Map<string, MetricData> {
    return new Map(this.metrics);
  }

  // 获取时间范围内的数据
  getDataInRange(name: string, startTime: number, endTime: number): DataPoint[] {
    return this.dataBuffer.filter(point => 
      point.name === name && 
      point.timestamp >= startTime && 
      point.timestamp <= endTime
    );
  }
}

// 2. 游戏性能监控
export class GamePerformanceMonitor {
  private analytics: RealTimeAnalytics;
  private startTime: number;
  private frameCount = 0;
  private lastFrameTime = 0;

  constructor(analytics: RealTimeAnalytics) {
    this.analytics = analytics;
    this.startTime = Date.now();
    this.setupMonitoring();
  }

  private setupMonitoring() {
    // 监控FPS
    this.monitorFPS();
    
    // 监控内存使用
    this.monitorMemory();
    
    // 监控网络延迟
    this.monitorNetworkLatency();
    
    // 监控WebSocket连接状态
    this.monitorWebSocketHealth();
  }

  private monitorFPS() {
    const measureFPS = () => {
      const now = performance.now();
      this.frameCount++;
      
      if (now - this.lastFrameTime >= 1000) {
        const fps = Math.round(this.frameCount * 1000 / (now - this.lastFrameTime));
        this.analytics.recordMetric('fps', fps);
        
        this.frameCount = 0;
        this.lastFrameTime = now;
      }
      
      requestAnimationFrame(measureFPS);
    };
    
    requestAnimationFrame(measureFPS);
  }

  private monitorMemory() {
    if ('memory' in performance) {
      setInterval(() => {
        const memInfo = (performance as any).memory;
        this.analytics.recordMetric('memory_used', memInfo.usedJSHeapSize / 1024 / 1024);
        this.analytics.recordMetric('memory_total', memInfo.totalJSHeapSize / 1024 / 1024);
        this.analytics.recordMetric('memory_limit', memInfo.jsHeapSizeLimit / 1024 / 1024);
      }, 5000);
    }
  }

  private monitorNetworkLatency() {
    setInterval(() => {
      const startTime = Date.now();
      
      // 发送ping请求
      fetch('/api/ping', { method: 'HEAD' })
        .then(() => {
          const latency = Date.now() - startTime;
          this.analytics.recordMetric('network_latency', latency);
        })
        .catch(() => {
          this.analytics.recordMetric('network_errors', 1);
        });
    }, 10000);
  }

  private monitorWebSocketHealth() {
    // 这里需要与WebSocket实例集成
    setInterval(() => {
      // 假设有全局WebSocket状态
      const wsState = (window as any).wsState;
      if (wsState) {
        this.analytics.recordMetric('websocket_connected', wsState.connected ? 1 : 0);
        this.analytics.recordMetric('websocket_reconnects', wsState.reconnectCount || 0);
      }
    }, 5000);
  }

  // 记录游戏特定指标
  recordGameMetric(name: string, value: number) {
    this.analytics.recordMetric(`game_${name}`, value);
  }

  // 记录用户行为
  recordUserAction(action: string, duration?: number) {
    this.analytics.recordMetric('user_actions', 1, { action });
    if (duration) {
      this.analytics.recordMetric('action_duration', duration, { action });
    }
  }
}

// 3. 业务指标监控
export class BusinessMetricsCollector {
  private analytics: RealTimeAnalytics;
  private sessionStart: number;
  private gameStats: GameSessionStats;

  constructor(analytics: RealTimeAnalytics) {
    this.analytics = analytics;
    this.sessionStart = Date.now();
    this.gameStats = {
      gamesPlayed: 0,
      totalBets: 0,
      totalWinnings: 0,
      averageGameDuration: 0,
      playerRetention: new Map()
    };
  }

  // 记录游戏开始
  recordGameStart(gameId: string, playerCount: number) {
    this.gameStats.gamesPlayed++;
    this.analytics.recordMetric('games_started', 1);
    this.analytics.recordMetric('players_per_game', playerCount);
    
    // 记录游戏开始时间
    this.analytics.recordMetric('game_start_time', Date.now(), { gameId });
  }

  // 记录游戏结束
  recordGameEnd(gameId: string, duration: number, results: GameResult[]) {
    this.analytics.recordMetric('games_completed', 1);
    this.analytics.recordMetric('game_duration', duration);
    
    // 更新平均游戏时长
    this.gameStats.averageGameDuration = 
      (this.gameStats.averageGameDuration * (this.gameStats.gamesPlayed - 1) + duration) / 
      this.gameStats.gamesPlayed;

    // 记录游戏结果
    results.forEach(result => {
      if (result.winnings > 0) {
        this.analytics.recordMetric('player_winnings', result.winnings);
        this.gameStats.totalWinnings += result.winnings;
      }
      
      this.analytics.recordMetric('player_bets', result.totalBet);
      this.gameStats.totalBets += result.totalBet;
    });
  }

  // 记录玩家行为
  recordPlayerAction(playerId: string, action: string, amount?: number) {
    this.analytics.recordMetric('player_actions', 1, { 
      playerId, 
      action 
    });
    
    if (amount) {
      this.analytics.recordMetric('action_amounts', amount, { 
        playerId, 
        action 
      });
    }

    // 更新玩家活跃度
    const now = Date.now();
    if (!this.gameStats.playerRetention.has(playerId)) {
      this.gameStats.playerRetention.set(playerId, {
        firstSeen: now,
        lastSeen: now,
        actionCount: 0
      });
    }
    
    const playerData = this.gameStats.playerRetention.get(playerId)!;
    playerData.lastSeen = now;
    playerData.actionCount++;
  }

  // 记录错误
  recordError(error: string, context?: Record<string, any>) {
    this.analytics.recordMetric('errors', 1, { 
      error, 
      ...context 
    });
  }

  // 获取会话统计
  getSessionStats(): GameSessionStats & { sessionDuration: number } {
    return {
      ...this.gameStats,
      sessionDuration: Date.now() - this.sessionStart
    };
  }

  // 生成报告
  generateReport(): BusinessReport {
    const now = Date.now();
    const sessionDuration = now - this.sessionStart;
    
    return {
      timestamp: now,
      sessionDuration,
      gamesPlayed: this.gameStats.gamesPlayed,
      averageGameDuration: this.gameStats.averageGameDuration,
      totalBets: this.gameStats.totalBets,
      totalWinnings: this.gameStats.totalWinnings,
      activePlayerCount: this.gameStats.playerRetention.size,
      playerRetentionRate: this.calculateRetentionRate(),
      errorRate: this.calculateErrorRate(),
      performanceMetrics: this.getPerformanceMetrics()
    };
  }

  private calculateRetentionRate(): number {
    const now = Date.now();
    const activeThreshold = 5 * 60 * 1000; // 5分钟
    
    let activeCount = 0;
    this.gameStats.playerRetention.forEach(data => {
      if (now - data.lastSeen < activeThreshold) {
        activeCount++;
      }
    });
    
    return this.gameStats.playerRetention.size > 0 ? 
      activeCount / this.gameStats.playerRetention.size : 0;
  }

  private calculateErrorRate(): number {
    const errorMetric = this.analytics.getMetric('errors');
    const actionMetric = this.analytics.getMetric('player_actions');
    
    if (!errorMetric || !actionMetric || actionMetric.count === 0) {
      return 0;
    }
    
    return errorMetric.count / actionMetric.count;
  }

  private getPerformanceMetrics(): PerformanceMetrics {
    return {
      averageFPS: this.analytics.getMetric('fps')?.avg || 0,
      averageLatency: this.analytics.getMetric('network_latency')?.avg || 0,
      memoryUsage: this.analytics.getMetric('memory_used')?.lastValue || 0,
      websocketHealth: this.analytics.getMetric('websocket_connected')?.lastValue || 0
    };
  }
}

// 4. 告警系统
export class AlertSystem extends EventEmitter {
  private rules: AlertRule[] = [];
  private notifications: AlertNotification[] = [];
  private maxNotifications = 100;

  addRule(rule: AlertRule) {
    this.rules.push(rule);
  }

  removeRule(ruleId: string) {
    this.rules = this.rules.filter(rule => rule.id !== ruleId);
  }

  processAlert(alert: AlertEvent) {
    const notification: AlertNotification = {
      id: this.generateId(),
      rule: alert.rule,
      value: alert.value,
      timestamp: alert.timestamp,
      acknowledged: false,
      severity: this.calculateSeverity(alert)
    };

    this.notifications.push(notification);
    if (this.notifications.length > this.maxNotifications) {
      this.notifications.shift();
    }

    this.emit('notification', notification);
    
    // 发送到外部系统
    this.sendToExternalSystems(notification);
  }

  private calculateSeverity(alert: AlertEvent): 'low' | 'medium' | 'high' | 'critical' {
    const rule = alert.rule;
    
    if (rule.metric === 'errors' || rule.metric === 'websocket_connected') {
      return 'critical';
    }
    
    if (rule.metric === 'fps' || rule.metric === 'memory_used') {
      return 'high';
    }
    
    return 'medium';
  }

  private sendToExternalSystems(notification: AlertNotification) {
    // 发送到日志系统
    console.warn('Alert:', notification);
    
    // 发送到监控系统（如果配置了）
    if (process.env.MONITORING_WEBHOOK) {
      fetch(process.env.MONITORING_WEBHOOK, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(notification)
      }).catch(console.error);
    }
  }

  acknowledgeAlert(alertId: string) {
    const notification = this.notifications.find(n => n.id === alertId);
    if (notification) {
      notification.acknowledged = true;
      this.emit('acknowledged', notification);
    }
  }

  getActiveAlerts(): AlertNotification[] {
    return this.notifications.filter(n => !n.acknowledged);
  }

  private generateId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 5. 数据导出器
export class DataExporter {
  private analytics: RealTimeAnalytics;
  private businessMetrics: BusinessMetricsCollector;

  constructor(analytics: RealTimeAnalytics, businessMetrics: BusinessMetricsCollector) {
    this.analytics = analytics;
    this.businessMetrics = businessMetrics;
  }

  // 导出CSV格式数据
  exportToCSV(metricName: string, startTime: number, endTime: number): string {
    const data = this.analytics.getDataInRange(metricName, startTime, endTime);
    
    let csv = 'timestamp,value\n';
    data.forEach(point => {
      csv += `${new Date(point.timestamp).toISOString()},${point.value}\n`;
    });
    
    return csv;
  }

  // 导出JSON格式报告
  exportReport(): string {
    const report = this.businessMetrics.generateReport();
    const metrics = Object.fromEntries(this.analytics.getAllMetrics());
    
    return JSON.stringify({
      report,
      metrics,
      exportTime: new Date().toISOString()
    }, null, 2);
  }

  // 自动定期导出
  startAutoExport(intervalMinutes: number = 60) {
    setInterval(() => {
      const report = this.exportReport();
      const filename = `poker_analytics_${Date.now()}.json`;
      
      // 保存到本地存储或发送到服务器
      localStorage.setItem(`export_${filename}`, report);
      
      console.log(`Analytics exported: ${filename}`);
    }, intervalMinutes * 60 * 1000);
  }
}

// 类型定义
interface DataPoint {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
}

interface MetricData {
  name: string;
  count: number;
  sum: number;
  min: number;
  max: number;
  avg: number;
  lastValue: number;
  lastUpdate: number;
  history: { value: number; timestamp: number }[];
}

interface AlertRule {
  id: string;
  metric: string;
  condition: 'greater_than' | 'less_than' | 'equals';
  threshold: number;
  message: string;
}

interface AlertEvent {
  rule: AlertRule;
  value: number;
  timestamp: number;
}

interface AlertNotification {
  id: string;
  rule: AlertRule;
  value: number;
  timestamp: number;
  acknowledged: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface GameSessionStats {
  gamesPlayed: number;
  totalBets: number;
  totalWinnings: number;
  averageGameDuration: number;
  playerRetention: Map<string, {
    firstSeen: number;
    lastSeen: number;
    actionCount: number;
  }>;
}

interface GameResult {
  playerId: string;
  winnings: number;
  totalBet: number;
}

interface BusinessReport {
  timestamp: number;
  sessionDuration: number;
  gamesPlayed: number;
  averageGameDuration: number;
  totalBets: number;
  totalWinnings: number;
  activePlayerCount: number;
  playerRetentionRate: number;
  errorRate: number;
  performanceMetrics: PerformanceMetrics;
}

interface PerformanceMetrics {
  averageFPS: number;
  averageLatency: number;
  memoryUsage: number;
  websocketHealth: number;
}

export {
  RealTimeAnalytics,
  GamePerformanceMonitor,
  BusinessMetricsCollector,
  AlertSystem,
  DataExporter
};
