// 注意：这个测试需要先将improved-game-engine.ts编译为JavaScript
// 或者直接在TypeScript环境中运行

const crypto = require('crypto');

// 模拟改进的算法（简化版本用于测试）
const NUMS = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14];
const SUITS = ["c", "d", "h", "s"];

function secureRandom() {
  const buffer = crypto.randomBytes(4);
  return buffer.readUInt32BE(0) / 0x100000000;
}

function secureRandomInt(max) {
  if (max <= 0) return 0;
  return Math.floor(secureRandom() * max);
}

function improvedFisherYatesShuffle(deck) {
  for (let i = deck.length - 1; i > 0; i--) {
    const j = secureRandomInt(i + 1);
    [deck[i], deck[j]] = [deck[j], deck[i]];
  }
}

function multipleShuffles(deck, rounds = 3) {
  for (let round = 0; round < rounds; round++) {
    improvedFisherYatesShuffle(deck);
  }
}

function validateShuffleQuality(deck) {
  if (deck.length < 4) return 1;
  
  let score = 1.0;
  let sameSuitAdjacent = 0;
  let sameNumberAdjacent = 0;
  let consecutiveNumbers = 0;
  
  for (let i = 0; i < deck.length - 1; i++) {
    const current = deck[i];
    const next = deck[i + 1];
    
    if (current.suit === next.suit) {
      sameSuitAdjacent++;
    }
    
    if (current.num === next.num) {
      sameNumberAdjacent++;
    }
    
    if (Math.abs(current.num - next.num) === 1) {
      consecutiveNumbers++;
    }
  }
  
  const expectedSameSuit = (deck.length - 1) / 4;
  const expectedSameNumber = (deck.length - 1) / 13;
  const expectedConsecutive = (deck.length - 1) * 2 / 13;
  
  const suitDeviation = Math.abs(sameSuitAdjacent - expectedSameSuit) / expectedSameSuit;
  const numberDeviation = Math.abs(sameNumberAdjacent - expectedSameNumber) / expectedSameNumber;
  const consecutiveDeviation = Math.abs(consecutiveNumbers - expectedConsecutive) / expectedConsecutive;
  
  score -= Math.min(0.3, suitDeviation * 0.1);
  score -= Math.min(0.3, numberDeviation * 0.1);
  score -= Math.min(0.3, consecutiveDeviation * 0.1);
  
  return Math.max(0, score);
}

function improvedRandomHands(len = 7, ensureQuality = true) {
  const deck = [];
  
  for (const suit of SUITS) {
    for (const num of NUMS) {
      deck.push({ num, suit });
    }
  }
  
  let shuffleAttempts = 0;
  const maxAttempts = 10;
  
  do {
    multipleShuffles(deck, 3);
    shuffleAttempts++;
    
    if (!ensureQuality || validateShuffleQuality(deck) >= 0.7) {
      break;
    }
    
    if (shuffleAttempts >= maxAttempts) {
      console.warn(`洗牌质量检查失败，已尝试${maxAttempts}次，使用当前结果`);
      break;
    }
  } while (shuffleAttempts < maxAttempts);
  
  const result = deck.slice(0, len);
  
  result.forEach(card => {
    card.shuffleMethod = 'improved-fisher-yates';
    card.shuffleRounds = 3;
    card.shuffleAttempts = shuffleAttempts;
    card.shuffleTimestamp = Date.now();
  });
  
  return result;
}

// 测试改进算法的随机性
function testImprovedRandomness(samples = 100000) {
  console.log('=== 改进算法随机性测试 ===');
  
  const values = [];
  for (let i = 0; i < samples; i++) {
    values.push(secureRandom());
  }
  
  // 均匀性测试
  const bins = 10;
  const expected = samples / bins;
  const observed = new Array(bins).fill(0);
  
  values.forEach(v => {
    const bin = Math.floor(v * bins);
    observed[bin]++;
  });
  
  let chiSquare = 0;
  for (let i = 0; i < bins; i++) {
    chiSquare += Math.pow(observed[i] - expected, 2) / expected;
  }
  
  console.log(`卡方统计量: ${chiSquare.toFixed(3)} (自由度: ${bins-1})`);
  console.log(`临界值(α=0.05): 16.919`);
  console.log(`均匀性测试: ${chiSquare < 16.919 ? '✅ 通过' : '❌ 失败'}`);
  
  // 独立性测试
  let runs = 1;
  for (let i = 1; i < Math.min(1000, values.length); i++) {
    if ((values[i] > 0.5) !== (values[i-1] > 0.5)) {
      runs++;
    }
  }
  
  const n = Math.min(1000, values.length);
  const expectedRuns = (2 * n) / 3;
  const variance = (16 * n - 29) / 90;
  const z = Math.abs(runs - expectedRuns) / Math.sqrt(variance);
  
  console.log(`游程数: ${runs}, 期望: ${expectedRuns.toFixed(1)}`);
  console.log(`Z统计量: ${z.toFixed(3)} (临界值: 1.96)`);
  console.log(`独立性测试: ${z < 1.96 ? '✅ 通过' : '❌ 失败'}`);
}

// 测试改进洗牌算法的公平性
function testImprovedShuffleAlgorithm(iterations = 50000) {
  console.log('\n=== 改进洗牌算法公平性测试 ===');
  
  const cardPositions = {};
  const deckSize = 52;
  
  for (let card = 0; card < deckSize; card++) {
    cardPositions[card] = new Array(deckSize).fill(0);
  }
  
  let totalQualityScore = 0;
  let qualityAttempts = 0;
  
  for (let i = 0; i < iterations; i++) {
    const deck = improvedRandomHands(52);
    
    // 统计质量分数
    const quality = validateShuffleQuality(deck);
    totalQualityScore += quality;
    qualityAttempts += deck[0].shuffleAttempts || 1;
    
    deck.forEach((card, position) => {
      const cardIndex = SUITS.indexOf(card.suit) * 13 + NUMS.indexOf(card.num);
      cardPositions[cardIndex][position]++;
    });
  }
  
  const expected = iterations / deckSize;
  let totalChiSquare = 0;
  let maxDeviation = 0;
  
  for (let card = 0; card < deckSize; card++) {
    let cardChiSquare = 0;
    for (let pos = 0; pos < deckSize; pos++) {
      const observed = cardPositions[card][pos];
      const deviation = Math.abs(observed - expected);
      maxDeviation = Math.max(maxDeviation, deviation);
      cardChiSquare += Math.pow(observed - expected, 2) / expected;
    }
    totalChiSquare += cardChiSquare;
  }
  
  console.log(`总卡方统计量: ${totalChiSquare.toFixed(3)}`);
  console.log(`最大偏差: ${maxDeviation.toFixed(1)} (期望: ${expected.toFixed(1)})`);
  console.log(`相对偏差: ${(maxDeviation/expected*100).toFixed(2)}%`);
  console.log(`平均质量分数: ${(totalQualityScore/iterations).toFixed(3)}`);
  console.log(`平均洗牌尝试次数: ${(qualityAttempts/iterations).toFixed(1)}`);
  
  const acceptableDeviation = expected * 0.05; // 5%的偏差
  console.log(`洗牌公平性: ${maxDeviation < acceptableDeviation ? '✅ 通过' : '❌ 失败'}`);
}

// 测试性能对比
function testPerformanceComparison() {
  console.log('\n=== 性能对比测试 ===');
  
  const iterations = 10000;
  
  // 测试原始Math.random()
  console.time('原始Math.random()洗牌');
  for (let i = 0; i < iterations; i++) {
    const deck = [];
    for (const suit of SUITS) {
      for (const num of NUMS) {
        deck.push({ num, suit });
      }
    }
    
    // 简单Fisher-Yates
    for (let j = deck.length - 1; j > 0; j--) {
      const k = Math.floor(Math.random() * (j + 1));
      [deck[j], deck[k]] = [deck[k], deck[j]];
    }
  }
  console.timeEnd('原始Math.random()洗牌');
  
  // 测试改进算法
  console.time('改进加密安全洗牌');
  for (let i = 0; i < iterations; i++) {
    improvedRandomHands(52, false); // 不进行质量检查以测试基础性能
  }
  console.timeEnd('改进加密安全洗牌');
  
  // 测试带质量检查的改进算法
  console.time('改进算法(含质量检查)');
  for (let i = 0; i < iterations; i++) {
    improvedRandomHands(52, true);
  }
  console.timeEnd('改进算法(含质量检查)');
}

// 运行所有测试
function runAllTests() {
  console.log('=== 改进发牌算法测试报告 ===\n');
  
  testImprovedRandomness();
  testImprovedShuffleAlgorithm();
  testPerformanceComparison();
  
  console.log('\n=== 测试总结 ===');
  console.log('1. 使用Node.js crypto模块的加密安全随机数生成器');
  console.log('2. 实现多重洗牌和质量检查机制');
  console.log('3. 添加审计日志和可追溯性');
  console.log('4. 保持向后兼容性');
  console.log('\n改进后的算法应该显著提升公平性和安全性。');
}

runAllTests();
