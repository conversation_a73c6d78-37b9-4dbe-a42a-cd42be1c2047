# 德州扑克应用部署指南 (Gitea镜像版)

本文档提供了使用Gitea仓库镜像部署德州扑克应用的步骤和说明。

## 前提条件

- 安装 [Docker](https://docs.docker.com/get-docker/)
- 安装 [Docker Compose](https://docs.docker.com/compose/install/)
- 已有权限访问 gitea.minidoudou.com 镜像仓库

## 部署步骤

### 1. 登录Gitea镜像仓库

```bash
# 登录到Gitea仓库
docker login gitea.minidoudou.com
# 输入用户名和密码
```

### 2. 拉取镜像并启动容器

在项目根目录下运行以下命令：

```bash
# 拉取最新镜像并启动容器（后台运行）
docker-compose pull
docker-compose up -d

# 查看容器日志
docker-compose logs -f
```

应用将在 http://localhost:8086 上运行。

### 3. 停止和移除容器

```bash
# 停止容器
docker-compose stop

# 停止并移除容器
docker-compose down

# 停止并移除容器及卷（清除所有数据）
docker-compose down -v
```

## 常见问题排查

### 查看容器状态

```bash
docker ps
```

### 查看容器日志

```bash
docker logs poker-app
```

### 进入容器内部

```bash
docker exec -it poker-app /bin/sh
```

## 镜像更新

当新版本镜像推送到Gitea仓库后，可以使用以下命令更新：

```bash
# 拉取最新镜像
docker-compose pull

# 使用新镜像重启服务
docker-compose up -d
```

## 生产环境配置建议

1. 配置合适的资源限制
   ```yaml
   # 在docker-compose.yml中添加以下配置
   services:
     poker-app:
       deploy:
         resources:
           limits:
             cpus: '0.50'
             memory: 512M
   ```

2. 配置自动重启策略
   ```yaml
   services:
     poker-app:
       restart: always
   ```

3. 配置HTTPS和反向代理（使用Nginx或Traefik）

4. 设置容器健康检查
   ```yaml
   services:
     poker-app:
       healthcheck:
         test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8086"]
         interval: 30s
         timeout: 5s
         retries: 3
         start_period: 10s
   ``` 