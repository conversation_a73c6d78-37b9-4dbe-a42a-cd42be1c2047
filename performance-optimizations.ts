// 性能优化方案

// 1. WebSocket连接池管理
class WebSocketPool {
  private static instance: WebSocketPool;
  private connections: Map<string, WebSocket> = new Map();
  private reconnectAttempts: Map<string, number> = new Map();
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  static getInstance(): WebSocketPool {
    if (!WebSocketPool.instance) {
      WebSocketPool.instance = new WebSocketPool();
    }
    return WebSocketPool.instance;
  }

  createConnection(roomId: string, token: string): Promise<WebSocket> {
    return new Promise((resolve, reject) => {
      const existingWs = this.connections.get(roomId);
      if (existingWs && existingWs.readyState === WebSocket.OPEN) {
        resolve(existingWs);
        return;
      }

      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.hostname;
      const port = window.location.protocol === 'https:' ? '' : ':8086';
      const url = `${protocol}//${host}${port}/ws`;

      const ws = new WebSocket(url, token);
      
      ws.onopen = () => {
        console.log(`WebSocket连接已建立: ${roomId}`);
        this.connections.set(roomId, ws);
        this.reconnectAttempts.delete(roomId);
        resolve(ws);
      };

      ws.onerror = (error) => {
        console.error(`WebSocket连接错误: ${roomId}`, error);
        reject(error);
      };

      ws.onclose = () => {
        console.log(`WebSocket连接已关闭: ${roomId}`);
        this.connections.delete(roomId);
        this.handleReconnect(roomId, token);
      };
    });
  }

  private handleReconnect(roomId: string, token: string) {
    const attempts = this.reconnectAttempts.get(roomId) || 0;
    if (attempts < this.maxReconnectAttempts) {
      this.reconnectAttempts.set(roomId, attempts + 1);
      setTimeout(() => {
        this.createConnection(roomId, token).catch(console.error);
      }, this.reconnectDelay * Math.pow(2, attempts)); // 指数退避
    }
  }

  getConnection(roomId: string): WebSocket | null {
    return this.connections.get(roomId) || null;
  }

  closeConnection(roomId: string) {
    const ws = this.connections.get(roomId);
    if (ws) {
      ws.close();
      this.connections.delete(roomId);
    }
  }

  closeAllConnections() {
    this.connections.forEach(ws => ws.close());
    this.connections.clear();
  }
}

// 2. 内存管理优化
class MemoryManager {
  private static instance: MemoryManager;
  private observers: IntersectionObserver[] = [];
  private timers: Set<number> = new Set();
  private eventListeners: Map<string, EventListener[]> = new Map();

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  // 创建可清理的定时器
  createTimer(callback: () => void, delay: number): number {
    const timerId = window.setTimeout(() => {
      callback();
      this.timers.delete(timerId);
    }, delay);
    this.timers.add(timerId);
    return timerId;
  }

  // 创建可清理的间隔定时器
  createInterval(callback: () => void, delay: number): number {
    const intervalId = window.setInterval(callback, delay);
    this.timers.add(intervalId);
    return intervalId;
  }

  // 清理定时器
  clearTimer(timerId: number) {
    window.clearTimeout(timerId);
    window.clearInterval(timerId);
    this.timers.delete(timerId);
  }

  // 添加事件监听器
  addEventListener(element: EventTarget, event: string, listener: EventListener) {
    element.addEventListener(event, listener);
    const key = `${element.constructor.name}-${event}`;
    if (!this.eventListeners.has(key)) {
      this.eventListeners.set(key, []);
    }
    this.eventListeners.get(key)!.push(listener);
  }

  // 清理所有资源
  cleanup() {
    // 清理定时器
    this.timers.forEach(timerId => {
      window.clearTimeout(timerId);
      window.clearInterval(timerId);
    });
    this.timers.clear();

    // 清理观察者
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];

    // 清理事件监听器
    this.eventListeners.clear();
  }
}

// 3. 缓存管理
class CacheManager {
  private static instance: CacheManager;
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private maxSize = 100;

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  set(key: string, data: any, ttl: number = 300000): void { // 默认5分钟TTL
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // 清理过期缓存
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// 4. 图片预加载和优化
class ImageOptimizer {
  private static instance: ImageOptimizer;
  private loadedImages: Set<string> = new Set();
  private loadingPromises: Map<string, Promise<void>> = new Map();

  static getInstance(): ImageOptimizer {
    if (!ImageOptimizer.instance) {
      ImageOptimizer.instance = new ImageOptimizer();
    }
    return ImageOptimizer.instance;
  }

  preloadImage(src: string): Promise<void> {
    if (this.loadedImages.has(src)) {
      return Promise.resolve();
    }

    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src)!;
    }

    const promise = new Promise<void>((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        this.loadedImages.add(src);
        this.loadingPromises.delete(src);
        resolve();
      };
      img.onerror = () => {
        this.loadingPromises.delete(src);
        reject(new Error(`Failed to load image: ${src}`));
      };
      img.src = src;
    });

    this.loadingPromises.set(src, promise);
    return promise;
  }

  preloadImages(srcs: string[]): Promise<void[]> {
    return Promise.all(srcs.map(src => this.preloadImage(src)));
  }

  // 预加载扑克牌图片
  preloadPokerAssets(): Promise<void[]> {
    const suits = ['c', 'd', 'h', 's'];
    const numbers = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14];
    const cardImages: string[] = [];

    suits.forEach(suit => {
      numbers.forEach(num => {
        cardImages.push(`/images/cards/${suit}${num}.png`);
      });
    });

    // 添加其他游戏资源
    cardImages.push(
      '/images/card-back.png',
      '/images/chip-blue.png',
      '/images/chip-red.png',
      '/images/chip-green.png',
      '/images/chip-black.png',
      '/images/dealer-button.png'
    );

    return this.preloadImages(cardImages);
  }
}

// 5. 防抖和节流工具
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      func(...args);
    };
    
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    timeout = window.setTimeout(later, wait);
  };
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 6. 性能监控
class PerformanceTracker {
  private static instance: PerformanceTracker;
  private metrics: Map<string, number[]> = new Map();

  static getInstance(): PerformanceTracker {
    if (!PerformanceTracker.instance) {
      PerformanceTracker.instance = new PerformanceTracker();
    }
    return PerformanceTracker.instance;
  }

  startTiming(label: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (!this.metrics.has(label)) {
        this.metrics.set(label, []);
      }
      
      const times = this.metrics.get(label)!;
      times.push(duration);
      
      // 保持最近100次记录
      if (times.length > 100) {
        times.shift();
      }
      
      console.log(`${label}: ${duration.toFixed(2)}ms`);
    };
  }

  getAverageTime(label: string): number {
    const times = this.metrics.get(label);
    if (!times || times.length === 0) return 0;
    
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }

  getMetrics(): { [key: string]: { avg: number; count: number } } {
    const result: { [key: string]: { avg: number; count: number } } = {};
    
    for (const [label, times] of this.metrics.entries()) {
      result[label] = {
        avg: this.getAverageTime(label),
        count: times.length
      };
    }
    
    return result;
  }
}

// 7. 导出所有优化工具
export {
  WebSocketPool,
  MemoryManager,
  CacheManager,
  ImageOptimizer,
  PerformanceTracker
};

// 8. 初始化性能优化
export function initializePerformanceOptimizations() {
  // 预加载图片资源
  ImageOptimizer.getInstance().preloadPokerAssets().catch(console.error);
  
  // 定期清理缓存
  setInterval(() => {
    CacheManager.getInstance().cleanup();
  }, 60000); // 每分钟清理一次
  
  // 页面卸载时清理资源
  window.addEventListener('beforeunload', () => {
    MemoryManager.getInstance().cleanup();
    WebSocketPool.getInstance().closeAllConnections();
  });
  
  // 监控内存使用
  if ('memory' in performance) {
    setInterval(() => {
      const memInfo = (performance as any).memory;
      const usedMB = Math.round(memInfo.usedJSHeapSize / 1024 / 1024);
      const totalMB = Math.round(memInfo.totalJSHeapSize / 1024 / 1024);
      
      if (usedMB > 100) { // 如果使用超过100MB
        console.warn(`内存使用较高: ${usedMB}MB / ${totalMB}MB`);
        // 可以触发垃圾回收或清理缓存
        CacheManager.getInstance().clear();
      }
    }, 30000); // 每30秒检查一次
  }
}
