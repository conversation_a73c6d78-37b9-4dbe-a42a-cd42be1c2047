name: Build and Push Docker Image

on:
  push:
    branches:
      - master

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Gitea Container Registry
        uses: docker/login-action@v2
        with:
          registry: ccr.ccs.tencentyun.com
          username: ${{ secrets.TENCENT_REGISTRY_USERNAME }}
          password: ${{ secrets.TENCENT_REGISTRY_PASSWORD }}

      - name: Generate version
        id: version
        run: |
          # 从VERSION文件获取主版本号，如果文件不存在则使用默认值1
          if [ -f "VERSION" ]; then
            MAJOR_VERSION=$(cat VERSION)
          else
            MAJOR_VERSION=1
          fi
          MINOR_VERSION=${{ github.run_number }}
          VERSION="$MAJOR_VERSION.$MINOR_VERSION"
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT
          echo "Generated version: $VERSION"

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: |
            ccr.ccs.tencentyun.com/langhai8045/holdem-poker-nodejs:latest
            ccr.ccs.tencentyun.com/langhai8045/holdem-poker-nodejs:${{ steps.version.outputs.VERSION }}