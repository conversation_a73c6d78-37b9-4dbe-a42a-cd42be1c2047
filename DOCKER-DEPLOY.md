# 德州扑克应用 Docker 部署指南

本文档提供了使用 Docker 部署德州扑克应用的步骤和说明。

## 前提条件

- 安装 [Docker](https://docs.docker.com/get-docker/)
- 安装 [Docker Compose](https://docs.docker.com/compose/install/)

## 部署步骤

### 1. 构建并启动容器

在项目根目录下运行以下命令：

```bash
# 构建并启动容器（后台运行）
docker-compose up -d

# 查看容器日志
docker-compose logs -f
```

应用将在 http://localhost:8086 上运行。

### 2. 停止和移除容器

```bash
# 停止容器
docker-compose stop

# 停止并移除容器
docker-compose down

# 停止并移除容器及卷（清除所有数据）
docker-compose down -v
```

## 构建选项

### 单独构建镜像

```bash
docker build -t poker-app .
```

### 运行单独的容器

```bash
docker run -d -p 8086:8086 --name poker-app poker-app
```

## 常见问题排查

### 查看容器状态

```bash
docker ps
```

### 查看容器日志

```bash
docker logs poker-app
```

### 进入容器内部

```bash
docker exec -it poker-app /bin/sh
```

## 生产环境配置建议

1. 使用环境变量管理敏感信息
2. 考虑使用 Docker Swarm 或 Kubernetes 进行集群部署
3. 配置 HTTPS（可使用 Nginx 代理）
4. 设置容器资源限制（内存、CPU）
5. 配置监控和日志收集系统

## 更新应用

```bash
# 拉取最新代码
git pull

# 重新构建并启动
docker-compose up -d --build
``` 