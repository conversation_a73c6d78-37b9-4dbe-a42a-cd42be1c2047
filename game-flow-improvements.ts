// 游戏流程优化方案

import { EventEmitter } from 'events';

// 1. 智能游戏状态管理
export class GameStateManager extends EventEmitter {
  private state: GameState;
  private history: GameState[] = [];
  private maxHistorySize = 100;

  constructor(initialState: GameState) {
    super();
    this.state = initialState;
  }

  // 状态更新与历史记录
  updateState(newState: Partial<GameState>, reason?: string) {
    const previousState = { ...this.state };
    
    // 保存历史
    this.history.push(previousState);
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    }

    // 更新状态
    this.state = { ...this.state, ...newState };
    
    // 触发事件
    this.emit('stateChanged', {
      previous: previousState,
      current: this.state,
      reason
    });

    // 自动保存关键状态
    if (this.isKeyState(newState)) {
      this.saveCheckpoint();
    }
  }

  // 回滚到上一个状态
  rollback(): boolean {
    if (this.history.length === 0) return false;
    
    const previousState = this.history.pop()!;
    this.state = previousState;
    this.emit('stateRolledBack', this.state);
    return true;
  }

  // 检查是否为关键状态
  private isKeyState(state: Partial<GameState>): boolean {
    return !!(state.phase || state.currentPlayer || state.pot);
  }

  // 保存检查点
  private saveCheckpoint() {
    localStorage.setItem('gameCheckpoint', JSON.stringify({
      state: this.state,
      timestamp: Date.now()
    }));
  }

  // 恢复检查点
  restoreCheckpoint(): boolean {
    try {
      const saved = localStorage.getItem('gameCheckpoint');
      if (!saved) return false;

      const { state, timestamp } = JSON.parse(saved);
      
      // 检查是否过期（30分钟）
      if (Date.now() - timestamp > 30 * 60 * 1000) {
        localStorage.removeItem('gameCheckpoint');
        return false;
      }

      this.state = state;
      this.emit('stateRestored', this.state);
      return true;
    } catch {
      return false;
    }
  }

  getState(): GameState {
    return { ...this.state };
  }

  getHistory(): GameState[] {
    return [...this.history];
  }
}

// 2. 智能超时管理
export class TimeoutManager {
  private timers: Map<string, NodeJS.Timeout> = new Map();
  private warnings: Map<string, NodeJS.Timeout> = new Map();
  private callbacks: Map<string, () => void> = new Map();

  setActionTimeout(
    playerId: string, 
    duration: number, 
    onTimeout: () => void,
    onWarning?: () => void
  ) {
    // 清除现有定时器
    this.clearTimeout(playerId);

    // 设置警告定时器（剩余10秒时）
    if (onWarning && duration > 10000) {
      const warningTimer = setTimeout(() => {
        onWarning();
        this.warnings.delete(playerId);
      }, duration - 10000);
      this.warnings.set(playerId, warningTimer);
    }

    // 设置超时定时器
    const timer = setTimeout(() => {
      onTimeout();
      this.clearTimeout(playerId);
    }, duration);

    this.timers.set(playerId, timer);
    this.callbacks.set(playerId, onTimeout);
  }

  clearTimeout(playerId: string) {
    const timer = this.timers.get(playerId);
    const warning = this.warnings.get(playerId);

    if (timer) {
      clearTimeout(timer);
      this.timers.delete(playerId);
    }

    if (warning) {
      clearTimeout(warning);
      this.warnings.delete(playerId);
    }

    this.callbacks.delete(playerId);
  }

  extendTimeout(playerId: string, additionalTime: number) {
    const callback = this.callbacks.get(playerId);
    if (!callback) return false;

    this.clearTimeout(playerId);
    this.setActionTimeout(playerId, additionalTime, callback);
    return true;
  }

  getRemainingTime(playerId: string): number {
    // 这里需要更复杂的实现来跟踪剩余时间
    return 0;
  }

  clearAll() {
    this.timers.forEach(timer => clearTimeout(timer));
    this.warnings.forEach(warning => clearTimeout(warning));
    this.timers.clear();
    this.warnings.clear();
    this.callbacks.clear();
  }
}

// 3. 智能断线重连
export class ReconnectionManager {
  private reconnectAttempts = 0;
  private maxAttempts = 5;
  private baseDelay = 1000;
  private gameState: GameStateManager;
  private isReconnecting = false;

  constructor(gameState: GameStateManager) {
    this.gameState = gameState;
  }

  async handleDisconnection(): Promise<boolean> {
    if (this.isReconnecting) return false;
    
    this.isReconnecting = true;
    
    // 保存当前状态
    this.gameState.saveCheckpoint();
    
    // 尝试重连
    while (this.reconnectAttempts < this.maxAttempts) {
      try {
        await this.attemptReconnection();
        this.reconnectAttempts = 0;
        this.isReconnecting = false;
        return true;
      } catch (error) {
        this.reconnectAttempts++;
        const delay = this.baseDelay * Math.pow(2, this.reconnectAttempts - 1);
        await this.sleep(delay);
      }
    }
    
    this.isReconnecting = false;
    return false;
  }

  private async attemptReconnection(): Promise<void> {
    // 实现重连逻辑
    return new Promise((resolve, reject) => {
      // 模拟重连
      setTimeout(() => {
        if (Math.random() > 0.3) { // 70%成功率
          resolve();
        } else {
          reject(new Error('重连失败'));
        }
      }, 1000);
    });
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  reset() {
    this.reconnectAttempts = 0;
    this.isReconnecting = false;
  }
}

// 4. 游戏动作验证器
export class ActionValidator {
  static validateBet(
    amount: number, 
    playerChips: number, 
    minBet: number, 
    maxBet: number
  ): ValidationResult {
    if (amount < 0) {
      return { valid: false, error: '下注金额不能为负数' };
    }
    
    if (amount > playerChips) {
      return { valid: false, error: '筹码不足' };
    }
    
    if (amount < minBet && amount !== playerChips) {
      return { valid: false, error: `最小下注金额为 ${minBet}` };
    }
    
    if (amount > maxBet) {
      return { valid: false, error: `最大下注金额为 ${maxBet}` };
    }
    
    return { valid: true };
  }

  static validateAction(
    action: PlayerAction, 
    gameState: GameState, 
    playerId: string
  ): ValidationResult {
    const player = gameState.players.find(p => p.id === playerId);
    if (!player) {
      return { valid: false, error: '玩家不存在' };
    }

    if (gameState.currentPlayer !== playerId) {
      return { valid: false, error: '不是你的回合' };
    }

    switch (action.type) {
      case 'fold':
        return { valid: true };
        
      case 'check':
        if (gameState.currentBet > player.currentBet) {
          return { valid: false, error: '无法过牌，需要跟注或弃牌' };
        }
        return { valid: true };
        
      case 'call':
        const callAmount = gameState.currentBet - player.currentBet;
        if (callAmount > player.chips) {
          return { valid: false, error: '筹码不足以跟注' };
        }
        return { valid: true };
        
      case 'bet':
      case 'raise':
        return this.validateBet(
          action.amount!, 
          player.chips, 
          gameState.minBet, 
          gameState.maxBet
        );
        
      case 'allin':
        return { valid: true };
        
      default:
        return { valid: false, error: '无效的动作' };
    }
  }
}

// 5. 游戏事件系统
export class GameEventSystem extends EventEmitter {
  private eventHistory: GameEvent[] = [];
  private maxHistorySize = 1000;

  logEvent(event: GameEvent) {
    event.timestamp = Date.now();
    event.id = this.generateEventId();
    
    this.eventHistory.push(event);
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }

    this.emit('gameEvent', event);
    this.emit(event.type, event);
  }

  getEventHistory(filter?: (event: GameEvent) => boolean): GameEvent[] {
    return filter ? this.eventHistory.filter(filter) : [...this.eventHistory];
  }

  getPlayerActions(playerId: string): GameEvent[] {
    return this.eventHistory.filter(event => 
      event.playerId === playerId && event.type.startsWith('player.')
    );
  }

  private generateEventId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 6. 自动操作管理
export class AutoActionManager {
  private autoActions: Map<string, AutoAction> = new Map();
  private timeoutManager: TimeoutManager;

  constructor(timeoutManager: TimeoutManager) {
    this.timeoutManager = timeoutManager;
  }

  setAutoAction(playerId: string, action: AutoAction) {
    this.autoActions.set(playerId, action);
  }

  clearAutoAction(playerId: string) {
    this.autoActions.delete(playerId);
  }

  checkAutoAction(playerId: string, gameState: GameState): PlayerAction | null {
    const autoAction = this.autoActions.get(playerId);
    if (!autoAction) return null;

    const player = gameState.players.find(p => p.id === playerId);
    if (!player) return null;

    switch (autoAction.type) {
      case 'auto_fold':
        return { type: 'fold' };
        
      case 'auto_check_fold':
        if (gameState.currentBet === player.currentBet) {
          return { type: 'check' };
        } else {
          return { type: 'fold' };
        }
        
      case 'auto_call':
        const callAmount = gameState.currentBet - player.currentBet;
        if (callAmount <= autoAction.maxAmount!) {
          return { type: 'call' };
        } else {
          return { type: 'fold' };
        }
        
      default:
        return null;
    }
  }
}

// 7. 游戏统计收集器
export class GameStatsCollector {
  private stats: Map<string, PlayerStats> = new Map();

  recordAction(playerId: string, action: PlayerAction, gameState: GameState) {
    const playerStats = this.getOrCreateStats(playerId);
    
    playerStats.totalActions++;
    
    switch (action.type) {
      case 'fold':
        playerStats.folds++;
        break;
      case 'call':
        playerStats.calls++;
        break;
      case 'bet':
      case 'raise':
        playerStats.raises++;
        playerStats.totalBet += action.amount || 0;
        break;
      case 'allin':
        playerStats.allins++;
        break;
    }
  }

  recordHandResult(playerId: string, result: HandResult) {
    const playerStats = this.getOrCreateStats(playerId);
    
    playerStats.handsPlayed++;
    
    if (result.won) {
      playerStats.handsWon++;
      playerStats.totalWinnings += result.amount;
      
      if (result.amount > playerStats.biggestWin) {
        playerStats.biggestWin = result.amount;
      }
    } else {
      playerStats.totalLosses += result.amount;
    }

    // 更新最佳牌型
    if (this.isHandBetter(result.handType, playerStats.bestHand)) {
      playerStats.bestHand = result.handType;
    }
  }

  private getOrCreateStats(playerId: string): PlayerStats {
    if (!this.stats.has(playerId)) {
      this.stats.set(playerId, {
        handsPlayed: 0,
        handsWon: 0,
        totalActions: 0,
        folds: 0,
        calls: 0,
        raises: 0,
        allins: 0,
        totalBet: 0,
        totalWinnings: 0,
        totalLosses: 0,
        biggestWin: 0,
        bestHand: 'high_card'
      });
    }
    return this.stats.get(playerId)!;
  }

  private isHandBetter(newHand: string, currentBest: string): boolean {
    const handRanks = [
      'high_card', 'pair', 'two_pair', 'three_of_a_kind', 
      'straight', 'flush', 'full_house', 'four_of_a_kind', 
      'straight_flush', 'royal_flush'
    ];
    
    return handRanks.indexOf(newHand) > handRanks.indexOf(currentBest);
  }

  getStats(playerId: string): PlayerStats | null {
    return this.stats.get(playerId) || null;
  }

  getAllStats(): Map<string, PlayerStats> {
    return new Map(this.stats);
  }
}

// 类型定义
interface GameState {
  phase: 'preflop' | 'flop' | 'turn' | 'river' | 'showdown';
  players: Player[];
  currentPlayer: string;
  pot: number;
  currentBet: number;
  minBet: number;
  maxBet: number;
  communityCards: Card[];
}

interface Player {
  id: string;
  name: string;
  chips: number;
  currentBet: number;
  cards: Card[];
  isActive: boolean;
}

interface Card {
  suit: string;
  num: number;
}

interface ValidationResult {
  valid: boolean;
  error?: string;
}

interface PlayerAction {
  type: 'fold' | 'check' | 'call' | 'bet' | 'raise' | 'allin';
  amount?: number;
}

interface AutoAction {
  type: 'auto_fold' | 'auto_check_fold' | 'auto_call';
  maxAmount?: number;
}

interface GameEvent {
  id?: string;
  type: string;
  playerId?: string;
  data: any;
  timestamp?: number;
}

interface HandResult {
  won: boolean;
  amount: number;
  handType: string;
}

interface PlayerStats {
  handsPlayed: number;
  handsWon: number;
  totalActions: number;
  folds: number;
  calls: number;
  raises: number;
  allins: number;
  totalBet: number;
  totalWinnings: number;
  totalLosses: number;
  biggestWin: number;
  bestHand: string;
}

export {
  GameStateManager,
  TimeoutManager,
  ReconnectionManager,
  ActionValidator,
  GameEventSystem,
  AutoActionManager,
  GameStatsCollector
};
