{"name": "poker", "version": "0.1.0", "private": true, "proxy": "http://localhost:8086", "dependencies": {"@ant-design/icons": "^6.0.0", "@emotion/is-prop-valid": "^1.3.1", "@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@koa/router": "^10.1.1", "@mui/material": "^5.6.0", "@reduxjs/toolkit": "^1.8.1", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "@types/jest": "^24.9.1", "@types/node": "^12.20.47", "@types/react": "^16.14.24", "@types/react-redux": "^7.1.23", "@types/styled-components": "^5.1.34", "antd": "^4.19.5", "colors": "^1.4.0", "jsonwebtoken": "^8.5.1", "koa": "^2.13.4", "koa-body": "^4.2.0", "koa-static": "^5.0.0", "koa-websocket": "^6.0.0", "memfs": "^4.17.0", "nacos": "^2.6.0", "rc-queue-anim": "^2.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-redux": "^7.2.8", "react-scripts": "5.0.0", "styled-components": "^6.1.17", "typescript": "~4.1.5", "uuid": "^8.3.2", "ws": "^8.5.0"}, "scripts": {"start": "node dist/src/server/app-server.js", "start:web": "react-scripts start", "build": "react-scripts build && npm run build:server", "build:server": "tsc -p server.tsconfig.json", "test": "react-scripts test", "eject": "react-scripts eject", "test:gameengine": "./node_modules/mocha/bin/mocha -r ts-node/register ./src/server/tests/game-engine.test.ts", "test:serverapi": "./node_modules/mocha/bin/mocha -r ts-node/register ./src/server/tests/serverapi.test.ts", "test:ws": "./node_modules/mocha/bin/mocha -r ts-node/register ./src/server/tests/websocket.test.ts", "test:dao": "./node_modules/mocha/bin/mocha -r ts-node/register ./src/server/tests/dao.test.ts", "test:room": "./node_modules/mocha/bin/mocha -r ts-node/register ./src/server/tests/room.user.test.ts", "test:autoplay": "./node_modules/mocha/bin/mocha -r ts-node/register ./src/server/tests/autoplay.test.ts", "test:autoplay-single": "./node_modules/mocha/bin/mocha -r ts-node/register ./src/server/tests/autoplay-single.ts", "watch-server": "nodemon --watch './src/server/***/*.ts' -e ts,tsx --exec 'ts-node' ./src/server/app-server.ts"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/chai": "^4.3.0", "@types/jsonwebtoken": "^8.5.8", "@types/koa": "^2.13.4", "@types/koa__router": "^8.0.11", "@types/koa-static": "^4.0.2", "@types/koa-websocket": "^5.0.7", "@types/mocha": "^9.1.0", "@types/react-dom": "^18.0.0", "@types/testing-library__react": "^10.2.0", "@types/uuid": "^8.3.4", "@types/websocket": "^1.0.5", "axios": "^0.26.1", "chai": "^4.3.6", "mocha": "^9.2.2", "nodemon": "^2.0.15", "ts-node": "^10.7.0", "websocket": "^1.0.34"}}