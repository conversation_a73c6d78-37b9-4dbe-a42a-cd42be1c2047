// 后端优化方案

import { Context } from 'koa';
import { RateLimiterMemory } from 'rate-limiter-flexible';

// 1. API响应缓存中间件
class APICache {
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private maxSize = 1000;

  middleware() {
    return async (ctx: Context, next: () => Promise<any>) => {
      // 只缓存GET请求
      if (ctx.method !== 'GET') {
        await next();
        return;
      }

      const cacheKey = `${ctx.url}:${ctx.header.authorization || 'anonymous'}`;
      const cached = this.get(cacheKey);
      
      if (cached) {
        ctx.body = cached;
        ctx.set('X-Cache', 'HIT');
        return;
      }

      await next();

      // 缓存成功响应
      if (ctx.status === 200 && ctx.body) {
        this.set(cacheKey, ctx.body, 60000); // 1分钟TTL
        ctx.set('X-Cache', 'MISS');
      }
    };
  }

  private set(key: string, data: any, ttl: number): void {
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  private get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  clear(): void {
    this.cache.clear();
  }
}

// 2. 请求限流中间件
const rateLimiter = new RateLimiterMemory({
  keyGenerator: (ctx: Context) => ctx.ip,
  points: 100, // 每个IP每分钟100个请求
  duration: 60, // 60秒窗口
});

const wsRateLimiter = new RateLimiterMemory({
  keyGenerator: (token: string) => token,
  points: 50, // 每个用户每分钟50个WebSocket消息
  duration: 60,
});

export const rateLimitMiddleware = async (ctx: Context, next: () => Promise<any>) => {
  try {
    await rateLimiter.consume(ctx.ip);
    await next();
  } catch (rejRes) {
    ctx.status = 429;
    ctx.body = { error: '请求过于频繁，请稍后再试' };
  }
};

// 3. 错误处理和日志中间件
export const errorHandlerMiddleware = async (ctx: Context, next: () => Promise<any>) => {
  const startTime = Date.now();
  
  try {
    await next();
    
    // 记录成功请求
    const duration = Date.now() - startTime;
    console.log(`✅ ${ctx.method} ${ctx.url} - ${ctx.status} - ${duration}ms`);
    
  } catch (error: any) {
    const duration = Date.now() - startTime;
    
    // 记录错误
    console.error(`❌ ${ctx.method} ${ctx.url} - ${duration}ms`, {
      error: error.message,
      stack: error.stack,
      ip: ctx.ip,
      userAgent: ctx.header['user-agent']
    });
    
    // 设置错误响应
    ctx.status = error.status || 500;
    ctx.body = {
      code: -1,
      error: process.env.NODE_ENV === 'production' 
        ? '服务器内部错误' 
        : error.message,
      timestamp: new Date().toISOString()
    };
  }
};

// 4. 数据验证中间件
export const validateMiddleware = (schema: any) => {
  return async (ctx: Context, next: () => Promise<any>) => {
    try {
      // 这里可以使用joi、yup等验证库
      // const validated = await schema.validate(ctx.request.body);
      // ctx.request.body = validated;
      await next();
    } catch (error: any) {
      ctx.status = 400;
      ctx.body = {
        code: -1,
        error: '请求参数验证失败',
        details: error.details || error.message
      };
    }
  };
};

// 5. 压缩中间件
import * as compress from 'koa-compress';

export const compressionMiddleware = compress({
  filter: (content_type: string) => {
    return /text|javascript|json|css|xml|svg/.test(content_type);
  },
  threshold: 2048, // 只压缩大于2KB的响应
  gzip: {
    flush: require('zlib').constants.Z_SYNC_FLUSH
  },
  deflate: {
    flush: require('zlib').constants.Z_SYNC_FLUSH,
  },
  br: false // 禁用brotli压缩以提高性能
});

// 6. 安全中间件
export const securityMiddleware = async (ctx: Context, next: () => Promise<any>) => {
  // 设置安全头
  ctx.set('X-Content-Type-Options', 'nosniff');
  ctx.set('X-Frame-Options', 'DENY');
  ctx.set('X-XSS-Protection', '1; mode=block');
  ctx.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // CORS处理
  ctx.set('Access-Control-Allow-Origin', process.env.ALLOWED_ORIGINS || '*');
  ctx.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  ctx.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (ctx.method === 'OPTIONS') {
    ctx.status = 200;
    return;
  }
  
  await next();
};

// 7. WebSocket优化
export class OptimizedWebSocketHandler {
  private connections: Map<string, Set<any>> = new Map();
  private messageQueue: Map<string, any[]> = new Map();
  private batchTimer: Map<string, NodeJS.Timeout> = new Map();
  private readonly BATCH_DELAY = 50; // 50ms批处理延迟

  addConnection(roomId: string, ws: any) {
    if (!this.connections.has(roomId)) {
      this.connections.set(roomId, new Set());
    }
    this.connections.get(roomId)!.add(ws);
  }

  removeConnection(roomId: string, ws: any) {
    const roomConnections = this.connections.get(roomId);
    if (roomConnections) {
      roomConnections.delete(ws);
      if (roomConnections.size === 0) {
        this.connections.delete(roomId);
        this.clearBatchTimer(roomId);
      }
    }
  }

  // 批量发送消息以减少网络开销
  broadcastToRoom(roomId: string, message: any) {
    if (!this.messageQueue.has(roomId)) {
      this.messageQueue.set(roomId, []);
    }
    
    this.messageQueue.get(roomId)!.push(message);
    
    // 设置批处理定时器
    if (!this.batchTimer.has(roomId)) {
      const timer = setTimeout(() => {
        this.flushMessages(roomId);
      }, this.BATCH_DELAY);
      this.batchTimer.set(roomId, timer);
    }
  }

  private flushMessages(roomId: string) {
    const messages = this.messageQueue.get(roomId);
    const connections = this.connections.get(roomId);
    
    if (!messages || !connections || messages.length === 0) {
      return;
    }

    // 合并消息
    const batchMessage = {
      type: 'batch',
      messages: messages,
      timestamp: Date.now()
    };

    // 发送给所有连接
    connections.forEach(ws => {
      if (ws.readyState === 1) { // WebSocket.OPEN
        try {
          ws.send(JSON.stringify(batchMessage));
        } catch (error) {
          console.error('WebSocket发送失败:', error);
          this.removeConnection(roomId, ws);
        }
      }
    });

    // 清理
    this.messageQueue.set(roomId, []);
    this.clearBatchTimer(roomId);
  }

  private clearBatchTimer(roomId: string) {
    const timer = this.batchTimer.get(roomId);
    if (timer) {
      clearTimeout(timer);
      this.batchTimer.delete(roomId);
    }
  }

  // 检查WebSocket消息频率
  async checkMessageRate(token: string): Promise<boolean> {
    try {
      await wsRateLimiter.consume(token);
      return true;
    } catch {
      return false;
    }
  }
}

// 8. 数据库连接池优化（如果使用数据库）
export class DatabasePool {
  private static instance: DatabasePool;
  private pool: any; // 实际的数据库连接池

  static getInstance(): DatabasePool {
    if (!DatabasePool.instance) {
      DatabasePool.instance = new DatabasePool();
    }
    return DatabasePool.instance;
  }

  async initialize() {
    // 初始化数据库连接池
    // this.pool = new Pool({
    //   host: process.env.DB_HOST,
    //   port: process.env.DB_PORT,
    //   database: process.env.DB_NAME,
    //   user: process.env.DB_USER,
    //   password: process.env.DB_PASSWORD,
    //   min: 2,
    //   max: 10,
    //   idleTimeoutMillis: 30000,
    //   connectionTimeoutMillis: 2000,
    // });
  }

  async query(sql: string, params?: any[]): Promise<any> {
    // 执行数据库查询
    // const client = await this.pool.connect();
    // try {
    //   const result = await client.query(sql, params);
    //   return result.rows;
    // } finally {
    //   client.release();
    // }
  }

  async close() {
    if (this.pool) {
      await this.pool.end();
    }
  }
}

// 9. 健康检查端点
export const healthCheckMiddleware = async (ctx: Context, next: () => Promise<any>) => {
  if (ctx.path === '/health') {
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '1.0.0'
    };
    
    ctx.body = health;
    return;
  }
  
  await next();
};

// 10. 优化的应用配置
export function createOptimizedApp() {
  const Koa = require('koa');
  const app = new Koa();
  
  // 应用中间件（顺序很重要）
  app.use(healthCheckMiddleware);
  app.use(securityMiddleware);
  app.use(compressionMiddleware);
  app.use(errorHandlerMiddleware);
  app.use(rateLimitMiddleware);
  app.use(new APICache().middleware());
  
  // 设置应用配置
  app.proxy = true; // 信任代理
  app.silent = process.env.NODE_ENV === 'production'; // 生产环境静默错误
  
  return app;
}

export {
  APICache,
  OptimizedWebSocketHandler,
  DatabasePool
};
