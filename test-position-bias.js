const { randomHands, rank, pokerTypeName } = require('./dist/src/server/utils/game-engine.js');

// 测试位置偏差 - 模拟多人游戏中不同位置的牌力
function testPositionBias(rounds = 10000, players = 6) {
  const playerStats = Array(players).fill(0).map(() => ({}));
  
  for (let round = 0; round < rounds; round++) {
    const allCards = randomHands(players * 2 + 5); // 每人2张手牌 + 5张公共牌
    const communityCards = allCards.slice(players * 2, players * 2 + 5);
    
    for (let p = 0; p < players; p++) {
      const playerCards = [
        allCards[p * 2],     // 第一张手牌
        allCards[p * 2 + 1], // 第二张手牌
        ...communityCards    // 公共牌
      ];
      
      const result = rank(playerCards);
      const typeName = pokerTypeName(result.type);
      playerStats[p][typeName] = (playerStats[p][typeName] || 0) + 1;
    }
  }
  
  console.log(`位置偏差测试 (${rounds}局, ${players}人):`);
  console.log('位置\t一对%\t两对%\t三条%\t同花%\t顺子%');
  
  for (let p = 0; p < players; p++) {
    const stats = playerStats[p];
    const pairRate = ((stats['一对'] || 0) / rounds * 100).toFixed(1);
    const twoPairRate = ((stats['两对'] || 0) / rounds * 100).toFixed(1);
    const threeRate = ((stats['三条'] || 0) / rounds * 100).toFixed(1);
    const flushRate = ((stats['同花'] || 0) / rounds * 100).toFixed(1);
    const straightRate = ((stats['顺子'] || 0) / rounds * 100).toFixed(1);
    
    console.log(`${p+1}\t${pairRate}\t${twoPairRate}\t${threeRate}\t${flushRate}\t${straightRate}`);
  }
}

testPositionBias();