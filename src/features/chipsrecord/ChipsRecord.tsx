import { Table } from "antd";
import { ColumnsType } from "antd/lib/table";
import { useAppSelector } from "../../app/hooks";
import { selectChipsRecord } from "./chipsRecordSlice";
import styled from "@emotion/styled";

export interface TableData {
  id: string;
  name: string;
  chips: number;
  buyIn: number;
  profit: number;
}

const StyledTable = styled(Table)`
  .ant-table-tbody > tr:nth-of-type(odd) {
    background-color: #fffbf0;
  }
  .ant-table-tbody > tr:nth-of-type(even) {
    background-color: #f9fff0;
  }
  .ant-table-tbody > tr > td {
    background-color: inherit !important;
  }
  .ant-table-thead > tr > th {
    font-size: 15px !important;
    font-weight: bold !important;
  }
  .profit-positive {
    color: #ff4d4f !important;
  }
  .profit-negative {
    color: #52c41a !important;
  }
  .profit-zero {
    color: #8c8c8c !important;
  }
` as any;

export function ChipsRecord() {
  const roomChipsRecords = useAppSelector(selectChipsRecord);
  const data: TableData[] = roomChipsRecords.chipsRecords.map((cr) => ({
    id: cr.id,
    name: cr.name,
    chips: cr.chips,
    buyIn: cr.buyIn,
    profit: cr.chips - cr.buyIn,
  }));
  const columns: ColumnsType<TableData> = [
    {
      title: "玩家",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "筹码",
      dataIndex: "chips",
      key: "chips",
      sorter: (a: TableData, b: TableData) => a.chips - b.chips,
    },
    {
      title: "买入",
      dataIndex: "buyIn",
      key: "buyIn",
      sorter: (a: TableData, b: TableData) => a.buyIn - b.buyIn,
    },
    {
      title: "盈亏",
      dataIndex: "profit",
      key: "profit",
      sorter: (a: TableData, b: TableData) => a.profit - b.profit,
      defaultSortOrder: "descend",
    },
  ];

  const getRowClassName = (record: TableData) => {
    if (record.profit > 0) {
      return "profit-positive";
    } else if (record.profit < 0) {
      return "profit-negative";
    }
    return "profit-zero";
  };

  return (
    <StyledTable
      rowKey={"id"}
      columns={columns}
      dataSource={data}
      pagination={false}
      rowClassName={getRowClassName}
    />
  );
}
