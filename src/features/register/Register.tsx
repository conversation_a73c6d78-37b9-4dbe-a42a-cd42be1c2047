import React, { useState } from 'react';
import { useAppSelector, useAppDispatch } from '../../app/hooks';
import { registerAsync, selectError } from './registerSlice';
import { Form, Input, Button, Card, Typography } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import QueueAnim from 'rc-queue-anim';

const { Title } = Typography;

export function Register() {
  const dispatch = useAppDispatch();
  const error = useAppSelector(selectError);

  const onFinish = (account: { name: string; password: string }) => {
    console.log(account);
    dispatch(registerAsync({ account, dispatch }));
  };

  return (
    <div
      className="flex flex-column flex-center flex1"
      style={{
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        minHeight: '100vh',
        padding: '20px'
      }}
    >
      <QueueAnim type="bottom" delay={300}>
        <div key="form">
          <Card
            style={{
              width: '100%',
              maxWidth: '400px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              borderRadius: '8px'
            }}
          >
            <div style={{ textAlign: 'center', marginBottom: '24px' }}>
              <Title level={2} style={{ color: '#1890ff', marginBottom: '8px' }}>
                欢迎加入
              </Title>
              <p style={{ color: '#666' }}>登录即注册，开始您的德州扑克之旅</p>
            </div>

            <Form
              name="basic"
              initialValues={{ remember: true }}
              autoComplete="off"
              onFinish={onFinish}
              size="large"
            >
              <Form.Item
                name="name"
                rules={[{ required: true, message: '请输入用户名' }]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="用户名"
                  style={{ borderRadius: '4px' }}
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[{ required: true, message: '请输入密码' }]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="密码"
                  style={{ borderRadius: '4px' }}
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  style={{
                    width: '100%',
                    height: '40px',
                    borderRadius: '4px',
                    background: 'linear-gradient(90deg, #1890ff 0%, #096dd9 100%)',
                    border: 'none',
                    fontSize: '16px'
                  }}
                >
                  登录即注册
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </div>
      </QueueAnim>
    </div>
  );
}
