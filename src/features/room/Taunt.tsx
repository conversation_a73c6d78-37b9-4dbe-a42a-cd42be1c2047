import { message, Menu, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { ws_sendMessage } from '../../app/websocket';
import axios from 'axios';

// 全局静态缓存变量，确保所有组件实例共享
let cachedTauntOptions: string[] = [];
// 移除lastFetchTime和CACHE_TTL，使缓存永久有效
// let lastFetchTime = 0;
// const CACHE_TTL = 60000; // 缓存有效期1分钟

interface TauntProps {
  targetUser?: string; // 目标用户名，如果不提供则为全局喊话
  placement?: 'bottomLeft' | 'bottomRight';
  onSelect?: () => void; // 添加选择后的回调函数
}

export function Taunt({ targetUser, placement = 'bottomLeft', onSelect }: TauntProps) {
  // 初始状态可以使用缓存的值，如果有
  const [tauntOptions, setTauntOptions] = useState<string[]>(cachedTauntOptions);
  // 加载状态基于是否有缓存
  const [loading, setLoading] = useState(cachedTauntOptions.length === 0);

  // 从nacos获取嘲讽选项
  useEffect(() => {
    const fetchTauntOptions = async () => {
      // 如果缓存已存在，直接使用缓存，不再检查过期时间
      if (cachedTauntOptions.length > 0) {
        setTauntOptions(cachedTauntOptions);
        setLoading(false);
        return;
      }
      
      setLoading(true);
      try {
        // 这里应该是从nacos获取配置的接口
        const response = await axios.get('/api/config/taunt-options');
        if (response.data && Array.isArray(response.data)) {
          // 更新缓存
          cachedTauntOptions = response.data;
          // 不再记录获取时间
          // lastFetchTime = Date.now();
          
          // 更新组件状态
          setTauntOptions(response.data);
        }
      } catch (error) {
        console.error('获取嘲讽选项失败:', error);
        // 如果获取失败但缓存存在，使用缓存
        if (cachedTauntOptions.length > 0) {
          setTauntOptions(cachedTauntOptions);
        }
      } finally {
        setLoading(false);
      }
    };

    // 只有当缓存为空时才需要获取，不再检查过期时间
    if (cachedTauntOptions.length === 0) {
      fetchTauntOptions();
    } else {
      setLoading(false);
    }
    
  }, []);

  const handleTaunt = (option: string) => {
    const tauntMessage = targetUser 
      ? `@${targetUser} ${option}`
      : option;
    
    ws_sendMessage(tauntMessage);
    
    // 移除成功提示
    // message.success('喊话已发送', 1);
    
    // 调用选择回调，用于关闭Dropdown
    if (onSelect) {
      onSelect();
    }
    
    console.log('喊话已发送，菜单将自动关闭');
  };

  // 如果正在加载，显示加载中状态
  if (loading) {
    return (
      <Menu style={{ minWidth: '120px', padding: '10px 0', textAlign: 'center' }}>
        <Menu.Item disabled>
          <Spin size="small" /> 加载中...
        </Menu.Item>
      </Menu>
    );
  }

  // 如果没有选项，显示提示
  if (tauntOptions.length === 0) {
    return (
      <Menu style={{ minWidth: '120px' }}>
        <Menu.Item disabled>暂无喊话选项</Menu.Item>
      </Menu>
    );
  }

  return (
    <Menu style={{ minWidth: '120px' }}>
      {tauntOptions.map((option, index) => (
        <Menu.Item key={index} onClick={() => handleTaunt(option)}>
          {option}
        </Menu.Item>
      ))}
    </Menu>
  );
} 