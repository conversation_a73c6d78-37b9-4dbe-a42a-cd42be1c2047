import { Button, Input, message, Popconfirm, Tooltip, Dropdown } from "antd";
import { Card as PokerCard } from "../../ApiType";
import {
  LoginOutlined,
  CopyOutlined,
  CoffeeOutlined,
  CaretRightOutlined,
  CheckOutlined,
  PauseOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  MessageOutlined,
} from "@ant-design/icons";
import { useEffect, useRef, useState } from "react";
import { User } from "./User";
import { Owner } from "./Owner";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import {
  selectSelf,
  selectRoomID,
  selectUsers,
  selectGame,
  selectRoom,
  getSelectSettleStatus,
  setSelectSettleTimes,
} from "./roomSlice";
import {
  ws_pauseGame,
  ws_settleTimes,
  ws_startGame,
  ws_userHangup,
  ws_userLeave,
  ws_userReady,
  ws_userWatch,
} from "../../app/websocket";
import { Poker } from "./Poker";
import { CountDown } from "./CountDown";
import { ChipsRecord } from "../chipsrecord/ChipsRecord";
import GameHistory from "../gamehistory/GameHistory";
import { Spectators } from "./Spectators";
import { Taunt } from "./Taunt";

export function Room() {
  const dispatch = useAppDispatch();
  const centerRef = useRef(null);
  const [showConfirm, setShowConfirm] = useState(false);
  const [zoom, setZoom] = useState(1);
  const [showSidebar, setShowSidebar] = useState(false);
  const [fixedSidebar, setFixedSidebar] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isLandscape, setIsLandscape] = useState(false);
  const roomid = useAppSelector(selectRoomID);
  const users = useAppSelector(selectUsers) || [];
  const room = useAppSelector(selectRoom);
  const self = useAppSelector(selectSelf);
  const game = useAppSelector(selectGame);
  const selectSettleStatus = useAppSelector(getSelectSettleStatus);
  const isSettling = game?.isSettling || false;
  const nextGameTime = Math.floor(
    ((game?.nextGameTime || Date.now()) - Date.now()) / 1000
  );
  const cards: PokerCard[] = [...(game?.boardCards || [])];
  const [tauntDropdownVisible, setTauntDropdownVisible] = useState(false);

  function setSettleTimes(times: number) {
    ws_settleTimes(times);
    dispatch(setSelectSettleTimes(false));
  }

  useEffect(() => {
    function handleResize() {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      // 移动端检测
      const isMobileDevice = width <= 768;
      setIsMobile(isMobileDevice);
      setIsLandscape(width > height);
      
      if (isMobileDevice) {
        // 移动端逻辑
        setZoom(1); // 移动端不使用缩放，用CSS响应式布局
        setShowSidebar(false); // 移动端默认隐藏侧边栏
      } else {
        // 桌面端逻辑（保持原有逻辑）
        const minWidth = 1200;
        const minHeight = 600;
        const ratio = Math.min(width / minWidth, height / minHeight);
        setZoom(Math.min(1, ratio));
        setShowSidebar(ratio > 0.8);
      }
    }
    
    window.addEventListener("resize", handleResize);
    window.addEventListener("orientationchange", handleResize);
    handleResize();
    
    return () => {
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("orientationchange", handleResize);
    };
  }, []);

  return (
    <div
      ref={centerRef}
      className={isMobile ? "room-container" : ""}
      style={{
        margin: isMobile ? 5 : 20,
        display: "flex",
        flexDirection: isMobile ? "column" : "row",
        flex: 1,
        overflow: "hidden",
        height: isMobile ? "100vh" : "auto",
      }}
    >
      {/* 侧边栏 - 移动端改为抽屉式 */}
      <div
        className={`card ${isMobile ? "" : ""}`}
        style={{
          marginRight: isMobile ? 0 : 10,
          overflow: "auto",
          minWidth: 200,
          display: showSidebar || fixedSidebar ? "flex" : "none",
          flexDirection: "column",
          position: isMobile ? "fixed" : "relative",
          top: isMobile ? 0 : "auto",
          left: isMobile ? 0 : "auto",
          right: isMobile ? 0 : "auto",
          bottom: isMobile ? 0 : "auto",
          zIndex: isMobile ? 1100 : "auto",
          background: isMobile ? "white" : "transparent",
          boxShadow: isMobile ? "0 0 20px rgba(0,0,0,0.3)" : "2px 2px 2px lightgray",
          width: isMobile ? "100vw" : "auto",
          height: isMobile ? "100vh" : "auto",
          padding: isMobile ? "10px" : "10px",
        }}
      >
        {isMobile && (
          <div style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "10px",
            borderBottom: "1px solid #e8e8e8",
            paddingBottom: "10px",
          }}>
            <h3 style={{ margin: 0, fontSize: "16px" }}>游戏信息</h3>
            <Button 
              size="small" 
              onClick={() => setFixedSidebar(false)}
              style={{ fontSize: "12px" }}
            >
              关闭
            </Button>
          </div>
        )}
        {isMobile ? (
          <div style={{ display: "flex", flexDirection: "column", height: "100%" }}>
            <div style={{ flex: 1, marginBottom: "10px" }}>
              <h4 style={{ fontSize: "14px", margin: "0 0 8px 0" }}>游戏历史</h4>
              <div style={{ height: "40vh", overflow: "auto" }}>
                <GameHistory />
              </div>
            </div>
            <div style={{ flex: 1 }}>
              <h4 style={{ fontSize: "14px", margin: "0 0 8px 0" }}>筹码记录</h4>
              <div style={{ height: "40vh", overflow: "auto" }}>
                <ChipsRecord />
              </div>
            </div>
          </div>
        ) : (
          <GameHistory />
        )}
      </div>
      <div
        className={`card ${isMobile ? "game-main-area" : ""}`}
        style={{
          marginRight: isMobile ? 0 : 10,
          flex: 4,
          position: "relative",
          zoom: isMobile ? 1 : zoom,
        }}
      >
        {/* 桌面端侧边栏按钮 */}
        <div style={{
          position: "absolute",
          top: 10,
          right: 10,
          zIndex: 1000,
          display: !isMobile && zoom <= 0.8 ? "flex" : "none",
        }}>
          <Button onClick={() => setFixedSidebar(!fixedSidebar)}>
            侧边栏
          </Button>
        </div>
        
        {/* 移动端快捷按钮 */}
        {isMobile && (
          <div style={{
            position: "absolute",
            top: 10,
            right: 10,
            zIndex: 1000,
            display: "flex",
            flexDirection: "column",
            gap: "5px",
            alignItems: "flex-end",
          }}>
            <Tooltip title="游戏历史">
              <Button
                size="small"
                onClick={() => setFixedSidebar(!fixedSidebar)}
                style={{ 
                  fontSize: "12px", 
                  padding: "0 8px",
                  minWidth: "50px",
                  height: "28px"
                }}
              >
                历史
              </Button>
            </Tooltip>
            <Tooltip title="向所有玩家喊话">
              <Dropdown 
                overlay={<Taunt onSelect={() => setTauntDropdownVisible(false)} />} 
                trigger={['click']}
                visible={tauntDropdownVisible}
                onVisibleChange={setTauntDropdownVisible}
              >
                <Button 
                  icon={<MessageOutlined />} 
                  size="small"
                  type="primary"
                  style={{ 
                    background: '#1890ff', 
                    borderColor: '#1890ff',
                    fontSize: "12px",
                    padding: "0 8px",
                    minWidth: "50px",
                    height: "28px"
                  }}
                >
                  喊话
                </Button>
              </Dropdown>
            </Tooltip>
          </div>
        )}
        <Spectators />
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            transform: `scale(${zoom})`,
            transformOrigin: "top left",
            width: `${100/zoom}%`,
            height: `${100/zoom}%`,
          }}
        >
          <div className="roominfo flex-row flex-center">
            <Input style={{ width: 90 }} value={roomid} readOnly />
            <Tooltip title="复制房间ID">
              <Button
                icon={<CopyOutlined />}
                style={{ marginRight: 10 }}
                onClick={() => {
                  if (navigator.clipboard) {
                    navigator.clipboard.writeText(roomid || "");
                    message.success("复制成功");
                  } else {
                    message.error("无法获取剪切板权限，请手动复制");
                  }
                }}
              />
            </Tooltip>
            {self?.isRoomOwner ? (
              room?.isGaming ? (
                <Tooltip title="下一场暂停游戏">
                  <Button
                    icon={<PauseOutlined />}
                    style={{ marginRight: 10 }}
                    onClick={ws_pauseGame}
                  >
                    暂停
                  </Button>
                </Tooltip>
              ) : (
                <Tooltip title="开始游戏">
                  <Button
                    type="primary"
                    icon={<CaretRightOutlined />}
                    style={{ marginRight: 10 }}
                    onClick={ws_startGame}
                  >
                    开始
                  </Button>
                </Tooltip>
              )
            ) : null}
            {!self?.isSpectator ? (
              self?.isReady ? (
                <Tooltip title="暂时不参与游戏">
                  <Button
                    icon={<CoffeeOutlined />}
                    style={{ marginRight: 10 }}
                    onClick={ws_userHangup}
                  >
                    休息
                  </Button>
                </Tooltip>
              ) : (
                <Tooltip title="准备">
                  <Button
                    type="primary"
                    icon={<CheckOutlined />}
                    style={{ marginRight: 10 }}
                    onClick={ws_userReady}
                  >
                    准备
                  </Button>
                </Tooltip>
              )
            ) : null}
            {!self?.isReady ? (
              !self?.isSpectator ? (
                <Tooltip title="进入观战模式">
                  <Button
                    icon={<EyeOutlined />}
                    style={{ marginRight: 10 }}
                    onClick={() => ws_userWatch(true)}
                  >
                    观战
                  </Button>
                </Tooltip>
              ) : (
                <Tooltip title="参与游戏">
                  <Button
                    type="primary"
                    icon={<EyeInvisibleOutlined />}
                    style={{ marginRight: 10 }}
                    onClick={() => ws_userWatch(false)}
                  >
                    参战
                  </Button>
                </Tooltip>
              )
            ) : null}

            <Popconfirm
              title="如果在游戏中，将会自动弃牌。确认离开？"
              okText="确定离开"
              cancelText="留下"
              onConfirm={() => ws_userLeave()}
              onCancel={() => setShowConfirm(false)}
              visible={showConfirm}
            >
              <Tooltip title="退出房间">
                <Button
                  icon={<LoginOutlined />}
                  onClick={() => {
                    setShowConfirm(true);
                  }}
                >
                  退出
                </Button>
              </Tooltip>
            </Popconfirm>
          </div>
          <div className={`flex1 flex-row flex-center ${isMobile ? "users-area" : ""}`}>
            {users.map((id) => (
              <User id={`${id}`} key={id} />
            ))}
          </div>
          <div className="flex1 flex-column flex-center">
            <div style={{ width: 100, height: 24, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              {isSettling ? (
                <CountDown time={nextGameTime} total={10} />
              ) : (
                <div style={{ height: '100%' }}>&nbsp;</div>
              )}
            </div>
            <div className="boards flex-row flex-center">
              {/* 始终显示5张位置的牌，无论实际公共牌有多少 */}
              {Array.from({ length: 5 }).map((_, i) => {
                  const card = cards[i] || null; // 如果实际没有牌，显示为null
                  
                  // 根据公共牌数量决定游戏阶段
                  // PreFlop: 0张公共牌
                  // Flop: 3张公共牌
                  // Turn: 4张公共牌
                  // River: 5张公共牌
                  const boardCardsCount = game?.boardCards?.length || 0;
                  
                  // 将牌数转换为对应的游戏阶段值
                  // 0张牌 = PreFlop(0)
                  // 3张牌 = Flop(1)
                  // 4张牌 = Turn(2)
                  // 5张牌 = River(3)
                  let gameStage = 0; // 默认为PreFlop
                  if (boardCardsCount >= 3 && boardCardsCount <= 5) {
                    gameStage = boardCardsCount - 2; // 3→1, 4→2, 5→3
                  }
                  
                  // 判断卡牌是否应该显示为翻转状态（背面朝上）
                  let shouldFlip = true;
                  
                  // 如果有实际的卡牌，根据游戏阶段决定是否翻开
                  if (card) {
                    // 在非结算阶段，按正常游戏逻辑处理
                    if (!isSettling) {
                      // 已经展示过的牌直接显示为正面朝上（没有动画）
                      if ((gameStage >= 2 && i <= 2) || (gameStage >= 3 && i <= 3)) {
                        shouldFlip = false; // 直接正面朝上
                      }
                      // 新发的牌保持背面朝上，等待动画翻开
                      else {
                        shouldFlip = true; // 保持背面朝上，等待动画翻开
                      }
                    } else {
                      // 在结算阶段，需要区分已经发过的牌和新发的牌
                      // 已经展示过的牌直接显示为正面朝上（没有动画）
                      if ((gameStage >= 2 && i <= 2) || (gameStage >= 3 && i <= 3)) {
                        shouldFlip = false; // 直接正面朝上
                      }
                      // 新发的牌保持背面朝上，等待动画翻开
                      else {
                        shouldFlip = true; // 保持背面朝上，等待动画翻开
                      }
                    }
                  }
                  
                  // 计算翻牌动画延迟时间
                  // Flop阶段的3张牌依次翻开
                  // Turn和River阶段各自的牌有自己的延迟
                  let revealDelay = 0;
                  if (card) {
                    // 移除!isSettling条件，让结算阶段的新牌也能播放翻牌动画
                    if (gameStage === 1 && i <= 2) {
                      revealDelay = i * 300; // 第1,2,3张牌分别延迟0ms,300ms,600ms
                    } else if (gameStage === 2 && i === 3) {
                      revealDelay = 300; // Turn牌延迟300ms
                    } else if (gameStage === 3 && i === 4) {
                      revealDelay = 300; // River牌延迟300ms
                    }
                  }
                  
                  return (
                    <Poker
                      card={card}
                      key={`board-card-${i}-round-${game?.boardCards.length || 0}`}
                      initialFlipped={shouldFlip}
                      revealDelay={revealDelay}
                      isBoard={true}
                    />
                  );
                })}
            </div>
            <div className="pots">底池: {game?.pots || 0}</div>
          </div>
          {selectSettleStatus ? (
            <div style={{ margin: "auto", padding: "20px 0" }}>
              <div>
                请选择发牌次数
                <span style={{ width: 100 }}>
                  <CountDown time={30} total={30} />
                </span>
              </div>
              <div>
                <Button
                  type="primary"
                  style={{ marginRight: 10 }}
                  onClick={() => setSettleTimes(1)}
                >
                  发一次
                </Button>
                <Button
                  type="primary"
                  style={{ marginRight: 10 }}
                  onClick={() => setSettleTimes(2)}
                >
                  发两次
                </Button>
              </div>
            </div>
          ) : null}
          <div className={`flex1 ${isMobile ? "owner-area" : ""}`}>
            <Owner zoom={zoom} isMobile={isMobile} />
            
            {/* 全局喊话按钮 - 移动端调整位置 */}
            {!isMobile && (
              <div style={{ 
                position: 'absolute', 
                left: '20px', 
                bottom: '20px', 
                zIndex: 1000 
              }}>
                <Tooltip title="向所有玩家喊话">
                  <Dropdown 
                    overlay={<Taunt onSelect={() => setTauntDropdownVisible(false)} />} 
                    trigger={['click']}
                    visible={tauntDropdownVisible}
                    onVisibleChange={setTauntDropdownVisible}
                  >
                    <Button 
                      icon={<MessageOutlined />} 
                      type="primary"
                      size="middle"
                      style={{ 
                        background: '#1890ff', 
                        borderColor: '#1890ff',
                        boxShadow: '0 2px 6px rgba(24, 144, 255, 0.3)'
                      }}
                    >
                      喊话
                    </Button>
                  </Dropdown>
                </Tooltip>
              </div>
            )}
          </div>
        </div>
      </div>
      {/* 右侧边栏 - 移动端隐藏，整合到左侧栏 */}
      {!isMobile && (
        <div className="card" style={{ 
          minWidth: 230, 
          overflow: "auto", 
          display: showSidebar || fixedSidebar ? "flex" : "none" 
        }}>
          <ChipsRecord />
        </div>
      )}
    </div>
  );
}
