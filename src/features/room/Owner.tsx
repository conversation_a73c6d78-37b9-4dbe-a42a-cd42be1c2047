import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Popover,
  Checkbox,
  Switch,
  message,
  Popconfirm
} from "antd";
import { useEffect, useRef, useState } from "react";
import { useAppSelector } from "../../app/hooks";
import {
  ws_overtime,
  ws_userBet,
  ws_userFold,
  ws_userRebuy,
} from "../../app/websocket";
import { CountDown } from "./CountDown";
import { Poker } from "./Poker";
import { BigBlind, Dealer, SmallBlind } from "./Symbol";
import { selectGame, selectRoom, selectSelf } from "./roomSlice";
import { card2html } from "../gamehistory/GameHistory";
const hintsound = require("../../assets/hint.wav");
const dealcardsound = require("../../assets/dealcard.wav");

// 修改事件接口为统一的GlobalTauntEvent
interface GlobalTauntEvent extends CustomEvent {
  detail: {
    sender: string;
    message: string;
    type: 'direct' | 'global';
    target?: string;
  };
}

export function Owner({ zoom, isMobile = false }: { zoom: number; isMobile?: boolean }) {
  const hintSoundRef = useRef<HTMLAudioElement>(null);
  const dealCardSoundRef = useRef<HTMLAudioElement>(null);

  const self = useAppSelector(selectSelf);
  const game = useAppSelector(selectGame);
  const room = useAppSelector(selectRoom);

  const selfAsUser = room?.users.find((u) => u.id == self?.id);

  const preBet = game?.preBet || 0;
  const raiseBet = game?.raiseBet || 0;
  const raiseBetDiff = game?.raiseBetDiff || 0;
  const pots = game?.pots || 0;
  const bb = game?.bb || 0;
  const reBuyLimit = game?.reBuyLimit || 1;
  const isSettling = game?.isSettling || false;

  const name = self?.name || "";
  const stack = self?.stack || 0;
  const bet = self?.bet || 0;
  const isActing = self?.isActing || false;
  const isWaiting =
    !self?.isActing &&
    !self?.isAllIn &&
    !self?.isFoled &&
    self?.isInCurrentGame;
  const leftTime = (self?.actionEndTime || Date.now()) - Date.now();
  const position = self?.position;
  // const isAllIn = self?.isAllIn || false;

  const canCheck = bet == preBet;
  const canCall = preBet > bet && stack + bet > preBet;
  const canRaise = game?.raiseUser != self?.id;
  const shouldAllIn = stack + bet <= preBet;
  const onlyRaiseAllIn = stack + bet <= raiseBet + raiseBetDiff;
  const minRaise = Math.min(stack, Math.max(bb, raiseBet + raiseBetDiff - bet));
  const maxRaise = stack;
  const has1_3 = stack >= pots / 3 && pots / 3 >= minRaise;
  const has1_2 = stack >= pots / 2 && pots / 2 >= minRaise;
  const has2_3 = stack >= (pots * 2) / 3 && (pots * 2) / 3 >= minRaise;
  const has3_4 = stack >= (pots * 3) / 4 && (pots * 3) / 4 >= minRaise;
  const has1_1 = stack >= pots && pots >= minRaise;
  const useBB = stack > 4 * bb && pots < 4 * bb;

  const chips2call = Math.min(stack, preBet - bet);

  const inGame = self?.isInCurrentGame && self?.isReady && !self?.isFoled;
  const overtimeCost = Math.min(
    bb,
    Math.max(1, pots / 4 / (game?.userCount || 1)),
    stack / (game?.userCount || 1)
  );

  const [raise, setRaise] = useState(0);
  const [now, setNow] = useState(0);
  const [autoCheck, setAutoCheck] = useState(false);
  const [autoRevealHand, setAutoRevealHand] = useState(true);

  // 喊话相关状态变量 - 仅保留发送者喊话相关内容
  const [showSenderPopover, setShowSenderPopover] = useState(false);
  const senderMessageRef = useRef<string>('');
  const senderTargetRef = useRef<string>('');
  const senderTypeRef = useRef<'direct' | 'global'>('global');
  const localNameRef = useRef<string>("");
  
  useEffect(() => {
    if (self?.name) {
      const localName = localStorage.getItem("userName") || "";
      localNameRef.current = localName;
    }
  }, [self?.name]);
  
  // 监听统一的喊话事件
  useEffect(() => {
    const handleGlobalTaunt = (event: Event) => {
      try {
        const customEvent = event as GlobalTauntEvent;
        const { sender, message, type, target } = customEvent.detail;
        
        console.log(`Owner收到喊话事件: sender=${sender}, message=${message}, type=${type}`);
        
        // 只有发送者角色才显示喊话弹窗
        const isSender = sender === self?.name;
        
        if (isSender) {
          console.log(`Owner匹配成功，显示喊话弹窗在发送者角色栏`);
          // 设置喊话内容到ref中
          senderMessageRef.current = message;
          senderTypeRef.current = type;
          if (target) {
            senderTargetRef.current = target;
          }
          // 显示弹窗
          setShowSenderPopover(true);
          
          // 5秒后自动隐藏
          setTimeout(() => {
            setShowSenderPopover(false);
          }, 5000);
        }
      } catch (error) {
        console.error('处理喊话事件失败:', error);
      }
    };
    
    // 添加自定义事件监听
    window.addEventListener('globalTaunt', handleGlobalTaunt);
    
    return () => {
      // 移除监听
      window.removeEventListener('globalTaunt', handleGlobalTaunt);
    };
  }, [self?.name]);
  
  // 解码HTML转义字符
  const decodeHtml = (html: string) => {
    return html
      .replace(/&amp;/g, "&")
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&nbsp;/g, " ");
  };
  
  // 显示发送者喊话的内容
  const getSenderTauntContent = () => {
    const decodedMessage = decodeHtml(senderMessageRef.current);
    if (senderTypeRef.current === 'direct') {
      return (
        <div
          dangerouslySetInnerHTML={{
            __html: `@<strong>${senderTargetRef.current}</strong>: <strong style="color: #1890ff">${decodedMessage}</strong>`,
          }}
        ></div>
      );
    } else {
      return (
        <div
          dangerouslySetInnerHTML={{
            __html: `<strong style="color: #1890ff">${decodedMessage}</strong>`,
          }}
        ></div>
      );
    }
  };

  useEffect(() => {
    if (hintSoundRef.current && isActing) {
      const audio: HTMLAudioElement = hintSoundRef.current;
      try {
        audio.play();
      } catch (ignore) {}
    }

    if (autoCheck && isActing) {
      if (canCheck) {
        ws_userBet(bet);
      } else {
        ws_userFold();
      }
    }
  }, [isActing]);

  useEffect(() => {
    setRaise(0);
    setAutoCheck(false);
  }, [game?.boardCards.length, game?.isSettling]);

  useEffect(() => {
    if (dealCardSoundRef.current && room?.isGaming) {
      const audio: HTMLAudioElement = dealCardSoundRef.current;
      audio.play();
    }
  }, [game?.boardCards.length, room?.isGaming]);

  // 确认Allin
  const confirmAllin = () => {
    ws_userBet(stack + bet);
  };

  return (
    <div className="flex-row flex-center" style={{ height: "100%" }}>
      <audio src={hintsound} autoPlay={false} ref={hintSoundRef} />
      <audio src={dealcardsound} autoPlay={false} ref={dealCardSoundRef} />
      <div className="flex3 flex flex-center">
        <div className={`owner ${!inGame ? "fold" : ""}`}>
          <Popover
            content={
              <div
                dangerouslySetInnerHTML={{
                  __html: `${card2html(
                    self?.maxCards || []
                  )} <strong style="color: #FF6F00">+$${self?.profits}</strong> `,
                }}
              ></div>
            }
            visible={self?.isWinner && (self?.profits || 0) >= 0}
          >
            <Popover
              content={getSenderTauntContent()}
              trigger="click"
              visible={showSenderPopover}
            >
              <Avatar>{name.length > 0 ? name[0] : ""}</Avatar>
            </Popover>
          </Popover>
          <div>{name}</div>
          <div className="stack">
            <span className="coins">$</span>
            {stack}
          </div>
          {stack + bet < reBuyLimit * bb &&
          (game?.isSettling || !self?.isInCurrentGame || self.isFoled) ? (
            <Button type="primary" onClick={() => ws_userRebuy()}>
              再次买入
            </Button>
          ) : null}
          {position != "" ? (
            <div className="position">
              {position === "SB" ? (
                <SmallBlind />
              ) : position === "BB" ? (
                <BigBlind />
              ) : position === "D" ? (
                <Dealer />
              ) : null}
            </div>
          ) : null}
        </div>
        <div
          style={{
            margin: '6px 0 0 0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: 11,
            color: '#888'
          }}
        >
          <Switch
            checked={autoRevealHand}
            onChange={setAutoRevealHand}
            size="small"
            style={{ marginRight: 4 }}
          />
          自动翻手牌
        </div>
      </div>
      <div className="flex3 flex flex-column" style={{ height: "100%" }}>
        <div className="bet">
          {self?.actionName}
          {bet == 0 ? null : (
            <span>
              <span className="coins">$</span>
              {bet}
            </span>
          )}
        </div>
        <div className="flex1 flex-row flex-center">
          <Poker
            key={`hand0-${autoRevealHand ? 'auto' : 'manual'}`}
            card={self?.hands[0] || null}
            index={0}
            showHand={isSettling && !selfAsUser?.hands[0]}
            initialFlipped={!autoRevealHand}
          />
          <Poker
            key={`hand1-${autoRevealHand ? 'auto' : 'manual'}`}
            card={self?.hands[1] || null}
            index={1}
            showHand={isSettling && !selfAsUser?.hands[1]}
            initialFlipped={!autoRevealHand}
          />
          {/* {[...(self?.hands || []), null, null].splice(0, 2).map((card, i) => (
            <Poker card={card} index={i} showHand={isSettling} key={`${i}`} />
          ))} */}
          <div>{self?.handsType}</div>
        </div>
        <div className="flex flex-row flex-center">
          {isActing ? (
            <Tooltip title={`点击加时，需支付其他玩家各$${overtimeCost}`}>
              <Button
                type="text"
                style={{ width: 300 }}
                onClick={() => {
                  ws_overtime();
                  setNow(now + 1);
                }}
              >
                <CountDown time={Math.floor(leftTime / 1000)} now={now} />
              </Button>
            </Tooltip>
          ) : (
            <div>&nbsp;</div>
          )}
        </div>
      </div>
      <div className="flex4 flex flex-colomn user-actions" style={{
        transform: `scale(${ zoom < 1 ? 0.8/zoom : 1})`,
        zoom: `${zoom < 1 ? 1/zoom : 1}`,
        flex: zoom < 1 ? 12 : 4,
        transformOrigin: "bottom left",
        userSelect: "none",
      }}>
        {isWaiting ? (
          <div>
            <Switch
              checked={autoCheck}
              onChange={() => setAutoCheck(!autoCheck)}
            />
            自动过牌或弃牌
          </div>
        ) : null}
        {isActing ? (
          <>
            <div className="flex1 flex flex-row flex-center main-btn">
              <Button type="primary" size="large" onClick={ws_userFold}>
                弃牌
              </Button>
              {canCheck ? (
                <Button
                  type="primary"
                  size="large"
                  onClick={() => ws_userBet(bet)}
                >
                  过牌
                </Button>
              ) : null}
              {canCall ? (
                <Button
                  type="primary"
                  size="large"
                  onClick={() => ws_userBet(chips2call + bet)}
                >
                  跟注 ${chips2call}
                </Button>
              ) : null}
              {shouldAllIn ? (
                <Button
                  type="primary"
                  size="large"
                  danger
                  onClick={() => ws_userBet(chips2call + bet)}
                >
                  AllIn ${chips2call}
                </Button>
              ) : canRaise ? (
                onlyRaiseAllIn ? (
                  <Button
                    type="primary"
                    size="large"
                    danger
                    onClick={() => ws_userBet(minRaise + bet)}
                  >
                    AllIn ${minRaise}
                  </Button>
                ) : (
                  <Button
                    type="primary"
                    size="large"
                    disabled={raise < minRaise}
                    onClick={() => ws_userBet(raise + bet)}
                  >
                    加注 ${raise}
                  </Button>
                )
              ) : null}
              <Popconfirm
                title={
                  <div style={{ 
                    width: '100%',
                    textAlign: 'center',
                    background: '#fff',
                    padding: '10px 0',
                    borderTop: '2px solid #e53935',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <div style={{
                      width: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '4px'
                    }}>
                      <p style={{ 
                        fontSize: '12px', 
                        margin: 0,
                        color: '#333',
                        fontWeight: 'normal',
                        textAlign: 'center'
                      }}>
                        确定要全下
                      </p>
                      <div style={{
                        fontWeight: 'bold',
                        fontSize: '18px',
                        color: '#e53935',
                        fontFamily: 'Arial, sans-serif',
                        margin: '3px 0',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <span style={{ fontSize: '14px', marginRight: '1px' }}>$</span>{stack}
                      </div>
                      <p style={{ 
                        fontSize: '12px', 
                        margin: 0,
                        color: '#333',
                        fontWeight: 'normal',
                        textAlign: 'center'
                      }}>
                        吗？
                      </p>
                    </div>
                  </div>
                }
                icon={null}
                onConfirm={confirmAllin}
                okText="确认"
                cancelText="取消"
                placement="top"
                overlayClassName="allin-popconfirm"
                okButtonProps={{ 
                  style: { 
                    background: '#4a69bd',
                    borderColor: '#4a69bd',
                    boxShadow: 'none'
                  } 
                }}
                cancelButtonProps={{
                  style: {
                    borderColor: '#ddd',
                    color: '#666'
                  }
                }}
              >
                <Button
                  type="primary"
                  size="large"
                  danger
                  style={{ marginLeft: '10px' }}
                >
                  <img 
                    src={require("../../assets/allin.png")} 
                    alt="All In" 
                    style={{ height: '20px', marginRight: '5px', verticalAlign: 'middle' }} 
                  />
                  ALL IN
                </Button>
              </Popconfirm>
            </div>
            {canRaise ? (
              <div className="flex1 flex flex-row flex-center chip-selection">
                {useBB ? (
                  <>
                    <Button onClick={() => setRaise(bb * 2)}>2BB</Button>
                    <Button onClick={() => setRaise(Math.floor(bb * 2.5))}>
                      2.5BB
                    </Button>
                    <Button onClick={() => setRaise(bb * 3)}>3BB</Button>
                    <Button onClick={() => setRaise(bb * 4)}>4BB</Button>
                  </>
                ) : (
                  <>
                    {has1_3 ? (
                      <Button onClick={() => setRaise(Math.ceil(pots / 3))}>
                        1/3
                      </Button>
                    ) : null}
                    {has1_2 ? (
                      <Button onClick={() => setRaise(Math.ceil(pots / 2))}>
                        1/2
                      </Button>
                    ) : null}
                    {has2_3 ? (
                      <Button
                        onClick={() => setRaise(Math.ceil((pots * 2) / 3))}
                      >
                        2/3
                      </Button>
                    ) : null}
                    {has3_4 ? (
                      <Button
                        onClick={() => setRaise(Math.ceil((pots * 3) / 4))}
                      >
                        3/4
                      </Button>
                    ) : null}
                    {has1_1 ? (
                      <Button onClick={() => setRaise(Math.ceil(pots))}>
                        1Pots
                      </Button>
                    ) : null}
                  </>
                )}
                <InputNumber
                  min={minRaise}
                  max={maxRaise}
                  value={raise}
                  onChange={(v) => setRaise(v!)}
                />
              </div>
            ) : null}

            <div className="flex1 flex flex-row flex-center">
              <span className="coins">$</span>
              <Slider
                min={minRaise}
                max={maxRaise}
                onChange={setRaise}
                style={{ width: 200 }}
              />
            </div>
          </>
        ) : null}
      </div>
    </div>
  );
}
