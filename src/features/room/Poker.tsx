import { But<PERSON> } from "antd";
import QueueAnim from "rc-queue-anim";
import { useEffect, useState, useRef } from "react";
import { Card as PokerCard } from "../../ApiType";
import { ws_userShowHands } from "../../app/websocket";
import { PlayingCard } from "../../components";

export function Poker({
  card,
  index = 0,
  showHand = false,
  isOpponent = false,
  initialFlipped = true,
  revealDelay = 0,
  isBoard = false,
}: {
  card: PokerCard | null;
  index?: number;
  showHand?: boolean;
  isOpponent?: boolean;
  initialFlipped?: boolean;
  revealDelay?: number;
  isBoard?: boolean;
}) {
  const empty = !card ? "empty" : "";
  const [isRaised, setIsRaised] = useState(false);
  const [isFlipped, setIsFlipped] = useState(initialFlipped);
  const prevCardRef = useRef<PokerCard | null>(null);
  
  function num2s(n: number) {
    switch (n) {
      case 14:
        return "A";
      case 13:
        return "K";
      case 12:
        return "Q";
      case 11:
        return "J";
      case 0:
        return "";
    }
    return n.toString();
  }
  
  function suit2s(suit: string) {
    switch (suit) {
      case "c":
        return "♣";
      case "d":
        return "♦";
      case "h":
        return "♥";
      case "s":
        return "♠";
    }
    return "";
  }
  
  useEffect(() => {
    // 只有当卡牌ID变化时才重置状态，避免其他更新影响
    if (card && prevCardRef.current) {
      const prevCardId = `${prevCardRef.current.suit}${prevCardRef.current.num}`;
      const currentCardId = `${card.suit}${card.num}`;
      
      if (prevCardId !== currentCardId) {
        setIsRaised(false);
        setIsFlipped(initialFlipped);
      }
    }
    
    // 更新前一张卡牌的引用
    prevCardRef.current = card;
  }, [card, initialFlipped]);

  useEffect(() => {
    // 处理自动翻牌，只对公共牌生效
    if (card && isFlipped && isBoard && revealDelay >= 0) {
      // 对于延迟为0的情况，使用最小延迟10ms确保渲染顺序正确
      const actualDelay = revealDelay === 0 ? 10 : revealDelay;
      
      const timer = setTimeout(() => {
        setIsFlipped(false); // 翻开卡牌
      }, actualDelay);
      
      return () => clearTimeout(timer);
    }
  }, [card, isFlipped, revealDelay, isBoard]);
  
  // 处理亮牌并触发上移效果
  const handleShowHands = () => {
    // 如果牌是背面朝上的，先翻到正面朝上
    if (isFlipped) {
      setIsFlipped(false);
    }
    
    // 设置上移效果并调用亮牌接口
    setIsRaised(true);
    ws_userShowHands(index);
  };
  
  // 恢复手动翻牌功能，但仅对非公共牌有效
  const handleFlip = () => {
    if (!isBoard) {
      setIsFlipped(!isFlipped);
    }
  };
  
  // 根据不同情况设置卡片尺寸 - 移动端使用small，桌面端保持原来逻辑
  const isMobile = window.innerWidth <= 768;
  const cardSize = isMobile ? "small" : (isOpponent ? "small" : (isBoard ? "normal" : "big"));
  
  // 根据洗牌算法设置卡片背面颜色
  const backColor = (card as any)?.deckStyle || 'blue';
  
  return (
    <div className={`poker-card ${empty} ${isOpponent ? 'opponent-card' : ''}`}>
      {card ? (
        <div className="content">
          {/* 根据是否为公共牌决定是否添加点击事件 */}
          {isBoard ? (
            <PlayingCard 
              size={cardSize}
              point={num2s(card?.num || 0)} 
              suit={suit2s(card?.suit || "")} 
              raised={isRaised}
              flipped={isFlipped}
              backColor={backColor}
            />
          ) : (
            <div onClick={handleFlip}>
              <PlayingCard 
                size={cardSize}
                point={num2s(card?.num || 0)} 
                suit={suit2s(card?.suit || "")} 
                raised={isRaised}
                flipped={isFlipped}
                backColor={backColor}
              />
            </div>
          )}
          {showHand ? (
            <div className="show-hand-button-container">
              <Button 
                type="primary" 
                size="small"
                className="show-hand-button"
                onClick={handleShowHands}
              >
                亮牌
              </Button>
            </div>
          ) : null}
        </div>
      ) : null}
    </div>
  );
}
