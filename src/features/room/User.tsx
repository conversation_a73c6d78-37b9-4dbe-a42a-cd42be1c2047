import { <PERSON><PERSON>, Dropdown, Popover, Tooltip } from "antd";
import { ApiOutlined, CoffeeOutlined } from "@ant-design/icons";
import { shallowEqual, useSelector } from "react-redux";
import { RootState } from "../../app/store";
import { CountDown } from "./CountDown";
import { Poker } from "./Poker";
import { AllIn, BigBlind, Dealer, SmallBlind } from "./Symbol";
import { card2html } from "../gamehistory/GameHistory";
import { useEffect, useState, useRef } from "react";
import { Taunt } from "./Taunt";

interface TauntEventDetail {
  from: string;
  to: string;
  content: string;
}

// 自定义事件类型
interface TauntCustomEvent extends CustomEvent {
  detail: TauntEventDetail;
}

interface SenderTauntEvent extends CustomEvent {
  detail: {
    sender: string;
    message: string;
    type: 'direct' | 'global';
    target?: string;
  };
}

// 定义新的统一事件接口
interface GlobalTauntEvent extends CustomEvent {
  detail: {
    sender: string;
    message: string;
    type: 'direct' | 'global';
    target?: string;
  };
}

export function User({ id }: { id: string }) {
  const [isCurrentUser, setIsCurrentUser] = useState(false);
  const localNameRef = useRef<string>("");
  
  const user = useSelector((state: RootState) => {
    const users = state.room.room?.users;
    if (!users) return null;
    const index = users.findIndex((u) => u.id == id);
    return users[index];
  }, shallowEqual);
  
  useEffect(() => {
    // 通过localStorage中存储的name来判断是否是当前用户
    const localName = localStorage.getItem("userName") || "";
    localNameRef.current = localName;
    setIsCurrentUser(user?.name === localName);
  }, [user?.name]);
  
  const name = user?.name || "";
  const stack = user?.stack || 0;
  const bet = user?.bet || 0;
  const isActing = user?.isActing || false;
  const actionEndTime = user?.actionEndTime || Date.now();
  const showHands = user?.hands && (user?.hands[0] || user?.hands[1]);
  const position = user?.position;
  const posComp = position ? (
    position === "SB" ? (
      <SmallBlind />
    ) : position === "BB" ? (
      <BigBlind />
    ) : position === "D" ? (
      <Dealer />
    ) : null
  ) : null;
  const inGame = user?.isInCurrentGame && !user?.isFoled;
  
  // 结算Popover状态
  const [showSettlePopover, setShowSettlePopover] = useState(false);
  
  // 喊话相关状态变量 - 仅保留发送者喊话相关内容
  const [showSenderPopover, setShowSenderPopover] = useState(false);
  const senderMessageRef = useRef<string>('');
  const senderTargetRef = useRef<string>('');
  const senderTypeRef = useRef<'direct' | 'global'>('global');
  const [dropdownVisible, setDropdownVisible] = useState(false);
  
  // 监听统一的喊话事件
  useEffect(() => {
    const handleGlobalTaunt = (event: Event) => {
      try {
        const customEvent = event as GlobalTauntEvent;
        const { sender, message, type, target } = customEvent.detail;
        
        console.log(`用户[${name}]收到喊话事件: sender=${sender}, message=${message}, type=${type}`);
        
        // 只有发送者角色才显示喊话弹窗
        const isSender = sender === name;
        
        if (isSender) {
          console.log(`匹配成功，显示喊话弹窗在发送者角色栏`);
          // 设置喊话内容到ref中
          senderMessageRef.current = message;
          senderTypeRef.current = type;
          if (target) {
            senderTargetRef.current = target;
          }
          // 显示弹窗
          setShowSenderPopover(true);
          
          // 5秒后自动隐藏
          setTimeout(() => {
            setShowSenderPopover(false);
          }, 5000);
        }
      } catch (error) {
        console.error('处理喊话事件失败:', error);
      }
    };
    
    // 添加自定义事件监听
    window.addEventListener('globalTaunt', handleGlobalTaunt);
    
    return () => {
      // 移除监听
      window.removeEventListener('globalTaunt', handleGlobalTaunt);
    };
  }, [name]);
  
  // 解码HTML转义字符
  const decodeHtml = (html: string) => {
    return html
      .replace(/&amp;/g, "&")
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&nbsp;/g, " ");
  };
  
  // 显示发送者喊话的内容
  const getSenderTauntContent = () => {
    const decodedMessage = decodeHtml(senderMessageRef.current);
    if (senderTypeRef.current === 'direct') {
      return (
        <div
          dangerouslySetInnerHTML={{
            __html: `@<strong>${senderTargetRef.current}</strong>: <strong style="color: #1890ff">${decodedMessage}</strong>`,
          }}
        ></div>
      );
    } else {
      return (
        <div
          dangerouslySetInnerHTML={{
            __html: `<strong style="color: #1890ff">${decodedMessage}</strong>`,
          }}
        ></div>
      );
    }
  };
  
  return (
    <Dropdown 
      overlay={<Taunt targetUser={name} placement="bottomRight" onSelect={() => setDropdownVisible(false)} />} 
      trigger={['contextMenu']}
      visible={dropdownVisible}
      onVisibleChange={setDropdownVisible}
    >
      <div className="user flex-column flex-center">
        <div
          className={`userinfo flex-column flex-center ${!inGame ? "fold" : ""}`}
        >
          <Popover
            content={
              <div
                dangerouslySetInnerHTML={{
                  __html: `${card2html(
                    user?.maxCards || []
                  )} <strong style="color: #FF6F00">+$${user?.profits}</strong> `,
                }}
              ></div>
            }
            visible={user?.isWinner && user.profits >= 0}
            onVisibleChange={(visible) => {
              if (user?.isWinner && user.profits >= 0) {
                setShowSettlePopover(visible);
              }
            }}
          >
            <Popover
              content={getSenderTauntContent()}
              visible={showSenderPopover}
            >
              <Avatar>{name.length > 0 ? name[0] : ""}</Avatar>
            </Popover>
          </Popover>
          <div>{name}</div>
          <div className="stack">
            <span className="coins">$</span>
            {stack}
          </div>
        </div>
        {isActing ? (
          <CountDown
            time={Math.floor((actionEndTime - Date.now()) / 1000)}
            now={Date.now()}
          />
        ) : (
          <div>&nbsp;</div>
        )}

        <div className="bet">
          {user?.actionName}
          {bet > 0 ? (
            <span>
              <span className="coins">$</span>
              {bet}
            </span>
          ) : (
            <div>&nbsp;</div>
          )}
        </div>
        {showHands ? (
          <div className={`card-box ${!isCurrentUser ? 'opponent-box' : ''}`}>
            <div className="handstype">{user?.handsType}</div>
            {user?.hands.map((card, i) => (
              <Poker
                card={card}
                key={`${card ? `${card.num}${card.suit}` : i}`}
                isOpponent={!isCurrentUser}
              />
            ))}
          </div>
        ) : null}

        <div className="status">
          {user?.isOffline ? (
            <Tooltip title="掉线">
              <ApiOutlined />
            </Tooltip>
          ) : !user?.isReady ? (
            <Tooltip title="挂起">
              <CoffeeOutlined />
            </Tooltip>
          ) : null}
        </div>
        {position ? <div className="position">{posComp}</div> : null}
        <div className="allin">{user?.isAllIn ? <AllIn /> : null}</div>
      </div>
    </Dropdown>
  );
}
