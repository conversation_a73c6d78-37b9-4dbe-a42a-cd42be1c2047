import { Card } from "../../ApiType";
import { useAppSelector } from "../../app/hooks";
import { selectGameHistory } from "./gameHistorySlice";
import { useState, useEffect } from "react";
import { ws_sendMessage } from "../../app/websocket";

export function card2html(cards: Card[]): string {
  return cards
    .map((card) => {
      const n = card.num;
      const s = card.suit;
      const ret =
        (n === 14
          ? "A"
          : n === 13
          ? "K"
          : n === 12
          ? "Q"
          : n === 11
          ? "J"
          : n === 10
          ? "T"
          : `${n}`) +
        (s === "c"
          ? "♣︎"
          : s === "d"
          ? "♦︎"
          : s === "h"
          ? "♥︎"
          : s === "s"
          ? "♠︎"
          : "");
      if (s === "d" || s === "h") {
        return `<span style="color: #ae2f11">${ret}</span>`;
      } else {
        return ret;
      }
    })
    .join("");
}

function prettify(log: string) {
  // 先替换牌面符号
  log = log.replace(/(\d+(c|d|h|s))/g, (c) => {
    const [_, num, s] = c.match(/(\d+)(\w)/) || [];
    const n = parseInt(num, 10);
    return card2html([{ num: n, suit: s }]);
  });

  // 检查是否为系统消息（如Flop, Turn, River等）
  if (log.match(/^(Flop|Turn|River|===)/)) {
    return {
      __html: `<div style="font-weight: bold; color: #555555; margin: 5px 0;">${log}</div>`
    };
  }
  
  // 添加亮牌和结算相关的系统消息判断
  // 如果消息包含特定格式（如 "亮牌" "win" "lose" "AllIn" 等）和HTML颜色代码，认为是系统消息
  if (
    log.includes('亮牌') || 
    log.includes('【喊话：') || 
    log.includes('win') || 
    log.includes('lose') || 
    log.includes('AllIn $') ||
    log.includes('#ae2f11') ||
    log.match(/[♥♠♦♣]/) && (log.includes('葫芦') || log.includes('三条') || log.includes('顺子') || log.includes('对子') || log.includes('高牌'))
  ) {
    return {
      __html: log
    };
  }
  
  // 首先检查是否是"用户名 喊话:"格式（已经应用样式的消息）
  // 这种消息已经有样式了，直接返回
  if (log.includes('喊话:') || log.includes('喊话：')) {
    return {
      __html: log
    };
  }
  
  // 匹配多种格式的玩家消息:
  // 1. <strong>用户名</strong>: 消息内容
  // 2. 用户名: 消息内容
  // 3. 数字: 消息内容 (例如 "12: @test2 要不要考虑全下?")
  const messageRegex1 = /<strong>(.*?)<\/strong>:\s+(.*?)$/;
  const messageRegex2 = /^([^:]+):\s+(.*?)$/;
  
  const match = log.match(messageRegex1) || log.match(messageRegex2);
  
  if (match) {
    const [_, username, messageContent] = match;
    
    // 检查是否是@某人的消息
    const atRegex = /^@(\S+)\s+(.*)$/;
    const atMatch = messageContent.match(atRegex);
    
    if (atMatch) {
      // @消息 - 定向喊话
      const [__, targetUser, tauntContent] = atMatch;
      return {
        __html: `
          <div style="margin: 5px 0; padding: 5px 8px; border-radius: 4px; background-color: #f0f8ff; border-left: 3px solid #1890ff;">
            <span style="margin-right: 4px; font-size: 12px;">🎯</span>
            <strong style="color: #1890ff;">${username}</strong>
            <span style="margin: 0 3px; color: #666;">对</span>
            <strong style="color: #1890ff;">${targetUser}</strong>
            <span style="margin: 0 3px; color: #666;">说：</span>
            <span style="font-weight: 500; color: #333;">${tauntContent}</span>
          </div>
        `
      };
    } else {
      // 一般消息，检查是否看起来像喊话
      // 不包含系统关键词的消息被视为喊话
      const systemKeywords = [
        '亮牌', '赢得', '加注', '跟注', '弃牌', '全下', '买入', '离开', '进入', '发牌',
        'won', 'raise', 'call', 'fold', 'all-in', 'all in', 'buy', 'leave', 'enter', 'deal',
        'win', 'lose', 'AllIn', '葫芦', '三条', '顺子', '对子', '高牌', '发', '次'
      ];
      
      // 注意：这里我们不把包含"全下"作为系统消息的关键词，因为玩家可能会在喊话中询问全下
      const lowerMessage = messageContent.toLowerCase();
      const isSystemMessage = systemKeywords.some(keyword => 
        lowerMessage.includes(keyword.toLowerCase()) && 
        // 特殊处理"全下"，允许"要不要考虑全下"这类问句
        !(keyword === '全下' && (lowerMessage.includes('要不要') || lowerMessage.includes('考虑')))
      );
      
      // 如果消息包含牌面符号或HTML颜色代码，也视为系统消息
      const containsCardOrHtmlCode = messageContent.match(/[♥♠♦♣]/) || messageContent.includes('#ae2f11');
      
      if (!isSystemMessage && !containsCardOrHtmlCode) {
        // 是喊话消息 - 全局喊话
        return {
          __html: `
            <div style="margin: 5px 0; padding: 5px 8px; border-radius: 4px; background-color: #fff7e6; border-left: 3px solid #fa8c16;">
              <span style="margin-right: 4px; font-size: 12px;">📢</span>
              <strong style="color: #fa8c16;">${username}</strong>
              <span style="margin: 0 3px; color: #666;">喊话：</span>
              <span style="font-weight: 500; color: #333;">${messageContent}</span>
            </div>
          `
        };
      }
    }
  }
  
  // 默认格式
  return {
    __html: log
  };
}

export default function GameHistory() {
  const [message, setMessage] = useState("");
  
  const handleSend = () => {
    if (!message.trim()) return;
    
    // 获取当前用户名，用于后续识别自己发送的消息
    const currentUser = localStorage.getItem("userName") || "";
    console.log(`发送消息: ${message}, 当前用户: ${currentUser}`);
    
    ws_sendMessage(message);
    setMessage("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
      handleSend();
    }
  };

  const logs = useAppSelector(selectGameHistory);
  return (
    <>
    <div style={{ 
      overflow: "auto", 
      flex: 1, 
      padding: "12px", 
      background: "#f9f9f9", 
      borderRadius: "8px",
      border: "1px solid #e8e8e8",
      maxHeight: "calc(100% - 50px)"
    }}>
      {logs.map((log, i) => (
        <div
          dangerouslySetInnerHTML={prettify(log)}
          key={logs.length - i}
          style={{ marginBottom: "8px" }}
        ></div>
      ))}
      </div>
      <div style={{ 
        display: "flex", 
        flexDirection: "row", 
        marginTop: "10px", 
        padding: "8px 0"
      }}>
        <input 
          placeholder="CMD/Ctrl+Enter发送"
          style={{ 
            flex: 1, 
            minWidth: 100, 
            padding: "8px 12px",
            borderRadius: "4px",
            border: "1px solid #d9d9d9"
          }} 
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
        />
        <button 
          style={{ 
            marginLeft: 10, 
            whiteSpace: "nowrap",
            padding: "0 16px",
            background: "#1890ff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer"
          }}
          onClick={handleSend}
        >
          &gt;
        </button>
      </div>
    </>
  );
}
