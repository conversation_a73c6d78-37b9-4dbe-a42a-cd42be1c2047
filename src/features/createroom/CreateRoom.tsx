import {
  Input,
  InputN<PERSON>ber,
  But<PERSON>,
  Card,
  Dropdown,
  Menu,
  Typography,
  Space,
  Tooltip,
  Row,
  Col,
} from "antd";
import { 
  DownOutlined, 
  CrownOutlined,
  UserOutlined,
  PlayCircleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import {
  createRoomAsync,
  joinRoomAsync,
  selectStatus,
} from "./createRoomSlice";
import { logout } from "../home/<USER>";
import { RecentGameRecords } from "./RecentGameRecords";

const { Text } = Typography;

// 自定义输入框样式
const inputStyle = {
  width: '120px',
  borderRadius: '20px',
  border: '1px solid #ff6b00',
  boxShadow: '0 0 0 2px rgba(255, 107, 0, 0.1)',
  transition: 'all 0.3s',
};

// 自定义输入框悬停样式
const inputHoverStyle = {
  borderColor: '#ff9500',
  boxShadow: '0 0 0 2px rgba(255, 107, 0, 0.2)',
};

// 自定义输入框聚焦样式
const inputFocusStyle = {
  borderColor: '#ff9500',
  boxShadow: '0 0 0 2px rgba(255, 107, 0, 0.2)',
};

// 自定义按钮样式
const buttonStyle = {
  width: '100%',
  height: '48px',
  borderRadius: '8px',
  background: 'linear-gradient(90deg, #40E0D0 0%, #20B2AA 100%)',
  border: 'none',
  fontSize: '18px',
  fontWeight: 'bold',
  marginTop: '16px'
};

export function CreateRoom() {
  const dispatch = useAppDispatch();

  const [smallBlind, setSmallBlind] = useState(1);
  const [buyIn, setBuyIn] = useState(200);
  const [reBuyLimit, setReBuyLimit] = useState(10);
  const [actionTimeout, setActionTimeout] = useState(30);
  const [roomid, setRoomID] = useState("");
  const status = useAppSelector(selectStatus);

  useEffect(() => {
    setBuyIn(smallBlind * 200);
  }, [smallBlind]);

  return (
    <div
      style={{
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        minHeight: '100vh',
        padding: '20px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        overflow: 'auto'
      }}
    >
      <div style={{ 
        width: '100%', 
        maxWidth: '800px',
        margin: '0 auto',
        padding: '20px 0'
      }}>
        <div style={{ textAlign: 'center', marginBottom: '24px' }}>
          <Dropdown
            overlay={
              <Menu>
                <Menu.Item key="0">
                  <a onClick={() => dispatch(logout())}>注销</a>
                </Menu.Item>
              </Menu>
            }
          >
            <a className="ant-dropdown-link" style={{ fontSize: '24px', color: '#40E0D0' }}>
              <UserOutlined style={{ marginRight: '8px' }} />
              {localStorage["name"]} <DownOutlined />
            </a>
          </Dropdown>
        </div>

        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Card
            title={
              <Space>
                <PlayCircleOutlined style={{ color: '#40E0D0', fontSize: '20px' }} />
                <span style={{ fontSize: '18px', fontWeight: 'bold' }}>加入游戏</span>
              </Space>
            }
            style={{
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              borderRadius: '12px',
              background: 'white',
              marginBottom: '20px'
            }}
          >
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <div style={{ background: '#f8f8f8', padding: '16px', borderRadius: '8px' }}>
                <Row align="middle" gutter={8}>
                  <Col span={6}>
                    <Text strong style={{ fontSize: '16px', color: '#333' }}>房间ID:</Text>
                  </Col>
                  <Col span={18}>
                    <Input
                      value={roomid}
                      onChange={(e) => setRoomID(e.target.value)}
                      style={inputStyle}
                      size="large"
                      placeholder="请输入房间ID"
                    />
                  </Col>
                </Row>
              </div>

              <Button
                type="primary"
                onClick={() => dispatch(joinRoomAsync(roomid))}
                loading={status.joinRoomStatus === "loading"}
                style={buttonStyle}
              >
                加入房间
              </Button>
            </Space>
          </Card>

          <Card
            title={
              <Space>
                <CrownOutlined style={{ color: '#40E0D0', fontSize: '20px' }} />
                <span style={{ fontSize: '18px', fontWeight: 'bold' }}>创建房间</span>
              </Space>
            }
            style={{
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              borderRadius: '12px',
              background: 'white',
              marginBottom: '20px'
            }}
          >
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <div style={{ background: '#f8f8f8', padding: '16px', borderRadius: '8px' }}>
                <Row align="middle" gutter={8}>
                  <Col span={6}>
                    <Text strong style={{ fontSize: '16px', color: '#333' }}>小盲/大盲:</Text>
                  </Col>
                  <Col span={18}>
                    <Space>
                      <InputNumber
                        min={1}
                        value={smallBlind}
                        onChange={(v) => setSmallBlind(v!)}
                        style={inputStyle}
                        size="large"
                      />
                      <Text style={{ fontSize: '16px' }}>/</Text>
                      <InputNumber
                        disabled={true}
                        min={1}
                        value={smallBlind * 2}
                        style={inputStyle}
                        size="large"
                      />
                    </Space>
                  </Col>
                </Row>
              </div>

              <div style={{ background: '#f8f8f8', padding: '16px', borderRadius: '8px' }}>
                <Row align="middle" gutter={8}>
                  <Col span={6}>
                    <Text strong style={{ fontSize: '16px', color: '#333' }}>买入金额:</Text>
                  </Col>
                  <Col span={18}>
                    <InputNumber
                      min={1}
                      value={buyIn}
                      onChange={(v) => setBuyIn(v!)}
                      style={inputStyle}
                      size="large"
                    />
                  </Col>
                </Row>
              </div>

              <div style={{ background: '#f8f8f8', padding: '16px', borderRadius: '8px' }}>
                <Row align="middle" gutter={8}>
                  <Col span={6}>
                    <Space>
                      <Text strong style={{ fontSize: '16px', color: '#333' }}>再次买入限制:</Text>
                      <Tooltip title="当筹码低于设定的大盲倍数时，允许再次买入">
                        <InfoCircleOutlined style={{ color: '#999' }} />
                      </Tooltip>
                    </Space>
                  </Col>
                  <Col span={18}>
                    <InputNumber
                      min={1}
                      value={reBuyLimit}
                      onChange={(v) => setReBuyLimit(v!)}
                      style={inputStyle}
                      size="large"
                    />
                    <Text type="secondary" style={{ display: 'block', marginTop: '8px' }}>
                      大盲时允许再次买入
                    </Text>
                  </Col>
                </Row>
              </div>

              <div style={{ background: '#f8f8f8', padding: '16px', borderRadius: '8px' }}>
                <Row align="middle" gutter={8}>
                  <Col span={6}>
                    <Space>
                      <Text strong style={{ fontSize: '16px', color: '#333' }}>操作倒计时:</Text>
                      <Tooltip title="设置玩家操作的时间限制，-1表示无限制">
                        <InfoCircleOutlined style={{ color: '#999' }} />
                      </Tooltip>
                    </Space>
                  </Col>
                  <Col span={18}>
                    <InputNumber
                      min={-1}
                      value={actionTimeout}
                      onChange={(v) => setActionTimeout(v!)}
                      style={inputStyle}
                      size="large"
                    />
                    <Text type="secondary" style={{ display: 'block', marginTop: '8px' }}>
                      秒 (-1 表示无倒计时)
                    </Text>
                  </Col>
                </Row>
              </div>

              <Button
                type="primary"
                onClick={() =>
                  dispatch(createRoomAsync({
                    sb: smallBlind,
                    buyIn,
                    reBuyLimit,
                    actionTimeout
                  }))
                }
                loading={status.createRoomStatus === "loading"}
                style={buttonStyle}
              >
                创建房间
              </Button>
            </Space>
          </Card>

          <div style={{ marginBottom: '20px' }}>
            <RecentGameRecords />
          </div>
        </Space>
      </div>
    </div>
  );
}
