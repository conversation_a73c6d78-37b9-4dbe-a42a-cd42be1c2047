import { Card } from "antd";
import Table, { ColumnsType } from "antd/lib/table";
import { SimpleChipsRecord } from "../../ApiType";
import { TableData } from "../chipsrecord/ChipsRecord";
import styled from "@emotion/styled";

const StyledTable = styled(Table)`
  .ant-table-tbody > tr:nth-of-type(odd) {
    background-color: #fffbf0;
  }
  .ant-table-tbody > tr:nth-of-type(even) {
    background-color: #f9fff0;
  }
  .ant-table-tbody > tr > td {
    background-color: inherit !important;
  }
  .ant-table-thead > tr > th {
    font-size: 15px !important;
    font-weight: bold !important;
  }
  .profit-positive {
    color: #ff4d4f !important;
  }
  .profit-negative {
    color: #52c41a !important;
  }
  .profit-zero {
    color: #8c8c8c !important;
  }
` as any;

export function RecentGameRecords() {
  let crs: {
    [roomid: string]: {
      date: number;
      records: SimpleChipsRecord[];
    };
  } = {};
  try {
    crs = JSON.parse(localStorage["chipsRecords"] || "({})");
  } catch (ignore) {}

  const arr: { roomid: string; date: number; records: SimpleChipsRecord[] }[] =
    [];
  Object.keys(crs).forEach((roomid) => {
    const cr = crs[roomid];
    arr.push({
      roomid,
      date: cr.date,
      records: cr.records,
    });
  });
  arr.sort((a, b) => b.date - a.date);

  const columns: any[] = [
    {
      title: "玩家",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "筹码",
      dataIndex: "chips",
      key: "chips",
      sorter: (a: TableData, b: TableData) => a.chips - b.chips,
    },
    {
      title: "买入",
      dataIndex: "buyIn",
      key: "buyIn",
      sorter: (a: TableData, b: TableData) => a.buyIn - b.buyIn,
    },
    {
      title: "盈亏",
      dataIndex: "profit",
      key: "profit",
      sorter: (a: TableData, b: TableData) => a.profit - b.profit,
      defaultSortOrder: "descend",
    },
  ];

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    }).replace(/\//g, "-");
  };

  const getRowClassName = (record: TableData) => {
    if (record.profit > 0) {
      return "profit-positive";
    } else if (record.profit < 0) {
      return "profit-negative";
    }
    return "profit-zero";
  };

  return (
    <div>
      <h1 style={{ marginTop: 30 }}>最近的游戏记录</h1>
      {arr.map((item) => {
        const data: TableData[] = item.records.map((cr) => ({
          id: cr.id,
          name: cr.name,
          chips: cr.chips,
          buyIn: cr.buyIn,
          profit: cr.chips - cr.buyIn,
        }));
        return (
          <Card
            title={`时间 ${formatDate(item.date)}`}
            bordered={false}
            key={item.roomid}
            style={{ marginBottom: 10 }}
          >
            <StyledTable
              rowKey={"id"}
              columns={columns}
              dataSource={data}
              pagination={false}
              rowClassName={getRowClassName}
            />
          </Card>
        );
      })}
    </div>
  );
}
