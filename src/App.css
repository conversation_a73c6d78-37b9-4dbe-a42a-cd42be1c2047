@import "~antd/dist/antd.css";

body {
  background-color: #ececec;
}

html,
body {
  padding: 0;
  margin: 0;
}

#root,
.App {
  width: 100vw;
  height: 100vh;
}

.card {
  border: "1px solid #f0f0f0";
  border-radius: 2px;
  background-color: white;
  padding: 10px;
  box-shadow: 2px 2px 2px lightgray;
  flex: 1;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex1 {
  flex: 1;
}
.flex2 {
  flex: 2;
}
.flex3 {
  flex: 3;
}
.flex4 {
  flex: 4;
}
.flex5 {
  flex: 5;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.poker-card {
  height: 90%;
  max-height: 160px;
  min-height: 80px;
  width: 110px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  margin: 0 8px;
  position: relative;
}

.poker-card .content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.poker-card.empty {
  border: 1px dashed gray;
  box-shadow: none;
  border-radius: 6px;
}

.poker-card .full {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.roominfo {
  width: 480px;
  margin: auto;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.user {
  height: 170px;
  width: 90px;
  margin: 0 25px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user .status {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 102;
}

.user .position {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 101;
}

.allin {
  position: absolute;
  bottom: 30px;
  z-index: 101;
}

.allin img {
  height: 40px;
}

.position img {
  height: 28px;
  width: auto;
  filter: drop-shadow(0px 1px 2px rgba(0,0,0,0.2));
}

.userinfo {
  border-radius: 4px;
  background-color: #99ddff;
  width: 100%;
  flex: 1;
  padding: 4px;
  text-align: center;
  position: relative;
}
.userinfo.fold {
  background-color: #e8e8e8;
  color: #838383;
}
.userinfo.allin {
  border: 1px solid red;
}

.userinfo .ant-avatar,
.owner .ant-avatar {
  color: #f56a00;
  background-color: #fde3cf;
}

.userinfo.fold .ant-avatar,
.owner.fold .ant-avatar {
  color: white;
  background-color: lightgray;
}

.user .card-box {
  position: absolute;
  width: 200px;
  height: 120px;
  bottom: 0px;
  display: flex;
  flex-direction: row;
  left: 50%;
  transform: translateX(-50%);
  justify-content: center;
  overflow: visible;
}

.card-box .poker-card {
  width: 80px;
  height: 110px;
  margin: 0;
  position: absolute;
  font-size: 14px;
  z-index: 3;
}

.card-box .poker-card:first-child {
  left: 30px;
  transform: rotate(-5deg);
}

.card-box .poker-card:last-child {
  right: 30px;
  transform: rotate(5deg);
}

.card-box .poker-card .playing-card.raised {
  z-index: 5;
}

.card-box .handstype {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  text-align: center;
}

.stack {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  min-width: 32px;
  margin-top: 4px;
  z-index: 100;
  font-weight: bold;
}

.coins {
  opacity: 0.8;
  user-select: none;
  margin-right: 2px;
  font-weight: normal;
}

.bet {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  z-index: 100;
  min-height: 24px;
  color: #333;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  padding: 2px 6px;
  margin: 2px 0;
}

.bet .coins {
  position: static;
  margin-right: 2px;
}

.boards {
  display: flex;
  margin: auto;
}

.pots {
  display: flex;
  max-width: 320px;
  height: 48px;
  margin: auto;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  padding: 0 10px;
}

.owner {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 12px;
  border-radius: 8px;
  margin: 0 auto 15px;
  /* height: 170px; */
  width: 90px;
}

.owner.fold {
  background-color: #e8e8e8;
  color: #838383;
}

/* 主角色位置标志样式 - 确保在右上角 */
.owner .position {
  position: absolute;
  top: -20px;
  right: -20px;
  z-index: 200;
  transform: scale(1.2);
}

/* 普通用户位置标志样式 */
.user .position {
  position: absolute;
  top: -20px;
  right: -20px;
  z-index: 200;
  transform: scale(1.2);
}

/* 调整图标样式 */
.symbol-icon {
  height: 56px;
  width: auto;
  filter: drop-shadow(0px 4px 6px rgba(0,0,0,0.6));
  transition: transform 0.2s ease;
}

/* 当鼠标悬停在角色上时，图标有微小放大效果 */
.user:hover .position .symbol-icon,
.owner:hover .position .symbol-icon {
  transform: scale(1.1);
}

.user-actions {
  display: flex;
  flex-direction: column;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.main-btn {
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.user-actions button {
  margin: 5px 8px;
  min-width: 90px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.main-btn button {
  margin: 5px 10px;
  height: 40px;
  min-width: 100px;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.user-actions button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.main-btn button.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.main-btn button.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.main-btn button.ant-btn-primary.ant-btn-dangerous {
  background: #ff4d4f;
  border-color: #ff4d4f;
}

.chip-selection {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 5px;
}

.user-actions .ant-input-number {
  margin: 5px 8px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.spectators {
  position: absolute;
  top: 5px;
  right: 5px;
}

/* 移动端观战者位置调整 */
@media (max-width: 768px) {
  .spectators {
    top: 5px;
    left: 5px; /* 移动端放到左上角 */
    right: auto;
  }
}

/* 调整亮牌按钮容器样式 */
.show-hand-button-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 6px;
}

.show-hand-button-container:hover {
  opacity: 1;
}

.show-hand-button {
  min-width: 60px !important;
  height: 32px !important;
  padding: 4px 16px !important;
  font-size: 14px !important;
  line-height: 24px !important;
  border-radius: 16px !important;
  background: #1890ff !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3) !important;
  transition: all 0.3s ease !important;
  color: white !important;
  font-weight: 500 !important;
}

.show-hand-button:hover {
  background: #40a9ff !important;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0,0,0,0.4) !important;
}

.show-hand-button:active {
  transform: scale(1);
  box-shadow: 0 2px 8px rgba(0,0,0,0.3) !important;
}

/* 非对手牌保持原有样式 */
.card-box:not(.opponent-box) .poker-card:first-child {
  left: 30px;
  transform: rotate(-5deg);
}

.card-box:not(.opponent-box) .poker-card:last-child {
  right: 30px;
  transform: rotate(5deg);
}

/* 完全重新设计对手牌的位置 - 将其放在角色栏容器之外但更近 */
.opponent-box {
  position: absolute;
  width: 130px;
  height: 90px;
  top: 100%;
  display: flex;
  flex-direction: row;
  left: 50%;
  transform: translateX(-50%);
  justify-content: space-between;
  padding: 0 5px;
  overflow: visible;
  z-index: 5;
  margin-top: -20px;
}

/* 调整对手牌的样式 - 确保完全垂直展示并保持标准比例 */
.opponent-box .poker-card {
  width: 55px;
  height: 80px;
  position: relative;
  left: auto !important;
  right: auto !important;
  margin: 0 2px;
  transform: none !important;
}

/* 修改对手牌内部的PlayingCard容器，确保正确的长宽比 */
.opponent-box .poker-card .playing-card {
  width: 55px !important;
  height: 80px !important;
  transform: none !important;
  transform-style: flat !important;
  perspective: none !important;
}

.opponent-box .poker-card .playing-card-inner,
.opponent-box .poker-card .playing-card-front {
  transform: none !important;
  transform-style: flat !important;
  perspective: none !important;
}

/* 调整对手用户状态栏，减小宽度并为显示在外部的牌提供适当空间 */
.user:has(.opponent-box) {
  margin-bottom: 70px;
  position: relative;
}

/* ============== 移动端适配样式 ============== */

/* 基础移动端样式 */
@media (max-width: 768px) {
  body {
    overflow-x: hidden;
  }
  
  #root,
  .App {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
  }
  
  /* 房间布局适配 */
  .room-container {
    margin: 5px !important;
    flex-direction: column !important;
    height: calc(100vh - 10px) !important;
  }
  
  /* 游戏主区域 */
  .game-main-area {
    flex: 1 !important;
    margin: 0 !important;
    position: relative;
  }
  
  /* 侧边栏移动端隐藏，改为抽屉式 */
  .sidebar-mobile-hidden {
    display: none !important;
  }
  
  /* 房间信息栏适配 */
  .roominfo {
    width: 100% !important;
    padding: 8px !important;
    flex-wrap: wrap;
    gap: 5px;
  }
  
  .roominfo .ant-input {
    width: 80px !important;
    font-size: 12px;
  }
  
  .roominfo .ant-btn {
    height: 32px !important;
    font-size: 12px !important;
    padding: 0 8px !important;
  }
  
  /* 用户区域适配 */
  .users-area {
    flex-wrap: wrap !important;
    justify-content: center !important;
    padding: 5px 0;
  }
  
  .user {
    margin: 0 3px 5px 3px !important;
    width: 70px !important;
    height: 140px !important;
  }
  
  .userinfo {
    padding: 2px !important;
    font-size: 11px !important;
  }
  
  .user .ant-avatar {
    width: 28px !important;
    height: 28px !important;
    font-size: 12px !important;
  }
  
  /* 扑克牌适配 */
  .poker-card {
    margin: 0 2px !important;
    max-height: 100px !important;
    width: 70px !important;
  }
  
  .boards {
    margin: 10px auto !important;
  }
  
  .boards .poker-card {
    max-height: 80px !important;
    width: 55px !important;
    margin: 0 1px !important;
  }
  
  /* 底池信息 */
  .pots {
    max-width: 250px !important;
    height: 36px !important;
    font-size: 14px !important;
    padding: 0 8px !important;
  }
  
  /* Owner区域适配 */
  .owner-area {
    padding: 5px !important;
  }
  
  .owner {
    width: 70px !important;
    padding: 8px !important;
  }
  
  .owner .ant-avatar {
    width: 32px !important;
    height: 32px !important;
    font-size: 14px !important;
  }
  
  /* 用户操作区域 */
  .user-actions {
    display: flex;
    flex-direction: column;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }
  
  /* 移动端操作区域主按钮行 */
  .user-actions .main-btn {
    margin-bottom: 6px !important;
    gap: 4px;
    flex-wrap: wrap;
  }
  
  /* 移动端按钮样式 */
  .user-actions .ant-btn {
    height: 40px !important;
    min-width: 60px !important;
    font-size: 12px !important;
    margin: 2px !important;
    border-radius: 6px !important;
    padding: 0 8px !important;
    /* 增强触摸体验 */
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    touch-action: manipulation;
  }
  
  /* 筹码选择区域 */
  .chip-selection {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: space-between !important;
    margin: 4px 0 !important;
    gap: 3px;
  }
  
  .chip-selection .ant-btn {
    flex: 1 !important;
    min-width: 45px !important;
    max-width: 65px !important;
    height: 36px !important;
    font-size: 11px !important;
    margin: 1px !important;
  }
  
  /* 输入框和滑块区域 */
  .user-actions .ant-input-number {
    height: 36px !important;
    font-size: 14px !important;
    margin: 4px 0 !important;
    width: 100% !important;
  }
  
  .user-actions .ant-slider {
    margin: 8px 0 4px 0 !important;
  }
  

  
  /* 调整对手牌的移动端显示 */
  .opponent-box {
    width: 100px !important;
    height: 70px !important;
    margin-top: -15px !important;
  }
  
  .opponent-box .poker-card {
    width: 45px !important;
    height: 65px !important;
  }
}

/* 小屏幕手机适配 (iPhone SE等) */
@media (max-width: 480px) {
  .roominfo .ant-input {
    width: 60px !important;
  }
  
  .user {
    width: 60px !important;
    height: 120px !important;
    margin: 0 2px 3px 2px !important;
  }
  
  .user .ant-avatar {
    width: 24px !important;
    height: 24px !important;
    font-size: 10px !important;
  }
  
  .boards .poker-card {
    max-height: 70px !important;
    width: 48px !important;
  }
  
  .owner {
    width: 60px !important;
  }
  
  .owner .ant-avatar {
    width: 28px !important;
    height: 28px !important;
  }
}

/* 横屏适配 */
@media (max-width: 768px) and (orientation: landscape) {
  /* 房间容器在横屏时调整 */
  .room-container {
    height: 100vh !important;
  }
  
  /* 房间信息栏在横屏时更紧凑 */
  .roominfo {
    padding: 4px !important;
    gap: 3px !important;
  }
  
  .roominfo .ant-btn {
    height: 28px !important;
    font-size: 11px !important;
    padding: 0 6px !important;
  }
  
  /* 用户区域横向滚动 */
  .users-area {
    flex-wrap: nowrap !important;
    overflow-x: auto !important;
    padding: 3px 5px !important;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }
  
  .users-area::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
  
  .user {
    min-width: 55px !important;
    margin: 0 2px !important;
    flex-shrink: 0;
  }
  
  /* 游戏区域在横屏时调整 */
  .boards {
    margin: 5px auto !important;
  }
  
  .boards .poker-card {
    max-height: 70px !important;
    width: 48px !important;
  }
  
  .pots {
    height: 28px !important;
    font-size: 12px !important;
    padding: 0 6px !important;
  }
  
  /* 操作区域在横屏时更紧凑 */
  .user-actions {
    padding: 8px 12px !important;
    max-height: 25vh !important;
  }
  
  .user-actions .ant-btn {
    height: 34px !important;
    font-size: 12px !important;
    min-width: 60px !important;
  }
  
  .user-actions .main-btn {
    margin-bottom: 6px !important;
  }
  
  .chip-selection .ant-btn {
    height: 32px !important;
    min-width: 45px !important;
    max-width: 60px !important;
  }
  
  .user-actions .ant-input-number {
    height: 32px !important;
  }
  
  /* Owner区域在横屏时调整 */
  .owner-area {
    padding-bottom: 120px !important; /* 为底部操作区域留空间 */
  }
  
  /* 横屏时Owner组件布局调整 */
  .owner-area .flex-row {
    align-items: flex-start !important;
  }
  
  /* 横屏时手牌区域调整 */
  .owner-area .flex3.flex.flex-column {
    min-width: 200px !important;
    margin: 0 10px !important;
  }
  
  /* 横屏时手牌间距调整 */
  .owner-area .flex1.flex-row.flex-center .poker-card {
    margin: 0 8px !important;
  }
  
  .owner {
    width: 60px !important;
    padding: 6px !important;
  }
  
  .owner .ant-avatar {
    width: 28px !important;
    height: 28px !important;
    font-size: 12px !important;
  }
}

/* 超小屏幕 */
@media (max-width: 360px) {
  .roominfo {
    font-size: 11px !important;
  }
  
  .roominfo .ant-btn {
    height: 28px !important;
    font-size: 11px !important;
    padding: 0 6px !important;
  }
  
  .user {
    width: 55px !important;
    height: 110px !important;
  }
  
  .boards .poker-card {
    max-height: 60px !important;
    width: 42px !important;
  }
}

/* ============== 通用触摸优化 ============== */
@media (max-width: 768px) {
  /* 移除所有触摸高亮 */
  * {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
  
  /* 保持文本输入和内容区域可选择 */
  input, textarea, .game-history-content {
    -webkit-user-select: text;
    user-select: text;
  }
  
  /* 增强按钮触摸体验 */
  button, .ant-btn {
    touch-action: manipulation;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  button:active, .ant-btn:active {
    transform: scale(0.95);
  }
  
  /* 增强扑克牌触摸体验 */
  .playing-card {
    touch-action: manipulation;
    transition: transform 0.2s ease;
  }
  
  .playing-card:active {
    transform: scale(0.95);
  }
  
  /* 增强头像点击体验 */
  .ant-avatar {
    touch-action: manipulation;
    transition: transform 0.2s ease;
  }
  
  .ant-avatar:active {
    transform: scale(0.9);
  }
  
  /* 滚动区域优化 */
  .ant-dropdown-menu, .user-actions, .game-history-content {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
}

/* 调整对手牌的样式，使其平铺展示 */
/* 已经使用新的定位方式，这些样式不再需要 */

/* 喊话气泡样式 */
.taunt-bubble {
  display: none;
}

/* 用户头像区域样式调整，确保喊话气泡有足够的空间 */
.user {
  position: relative;
  margin-top: 15px;
}

/* 聊天喊话消息样式 */
.taunt-message {
  margin: 8px 0;
  padding: 8px 12px;
  border-radius: 6px;
  background-color: #f0f2f5;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-left: 4px solid #ff9800;
}

/* 喊话消息悬停效果 */
.taunt-message:hover {
  background-color: #f6f6f6;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

/* 添加喊话图标 */
.taunt-message::before {
  content: "📢";
  margin-right: 6px;
  font-size: 14px;
}

/* 定向@喊话特殊样式 */
.taunt-message.at-message {
  background-color: #e6f7ff;
  border-left: 4px solid #1890ff;
}

/* 定向喊话图标 */
.taunt-message.at-message::before {
  content: "🎯";
  margin-right: 6px;
  font-size: 14px;
}

/* 喊话内容样式 */
.taunt-content {
  font-weight: bold;
  display: inline-block;
  margin-top: 5px;
  padding: 4px 10px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  color: #1f1f1f;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 喊话Popover样式 */
.taunt-popover-content {
  min-width: 150px;
  max-width: 250px;
}

.taunt-popover-content p {
  margin-bottom: 8px;
}

.taunt-popover-content .taunt-content {
  font-weight: bold;
  font-size: 14px;
  color: #1890ff;
  background-color: #f0f8ff;
  padding: 5px 8px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

/* 调整喊话Popover样式，使其与结算Popover一致 */
.ant-popover-inner-content {
  padding: 12px 16px;
  border-radius: 4px;
}

/* 设置喊话内容的样式 */
.ant-popover-inner-content strong {
  font-weight: 600;
}

/* 确保喊话内容可见性 */
.ant-popover {
  z-index: 1050;
}

/* 修改喊话内容的样式，不再使用 */
.taunt-popover-content {
  display: none;
}

/* Allin确认弹窗样式 */
.allin-popconfirm .ant-popover-content {
  width: 120px;
}

.allin-popconfirm .ant-popover-inner {
  min-width: auto;
  box-shadow: 0 2px 8px rgba(0,0,0,0.12);
  border-radius: 4px;
  overflow: hidden;
}

.allin-popconfirm .ant-popover-arrow {
  display: none;
}

.allin-popconfirm .ant-popover-inner-content {
  padding: 0;
}

.allin-popconfirm .ant-popover-message {
  padding: 0;
  margin: 0;
}

.allin-popconfirm .ant-popover-message-title {
  padding: 0;
  margin: 0;
}

.allin-popconfirm .ant-popover-buttons {
  display: flex;
  justify-content: center;
  text-align: center;
  padding: 8px;
  margin: 0;
  background-color: #f9f9f9;
  border-top: 1px solid #f0f0f0;
}

.allin-popconfirm .ant-popover-buttons button {
  min-width: 48px;
  margin: 0 3px;
  font-size: 12px;
  height: 24px;
  padding: 0 8px;
  line-height: 22px;
}
