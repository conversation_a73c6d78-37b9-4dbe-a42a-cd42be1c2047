import Router from '@koa/router';
import { NacosConfigClient } from 'nacos';

// 存储嘲讽选项的缓存
let tauntOptionsCache: string[] = [
  '你的牌打得真好！',
  '要不要考虑全下？',
  '我已经看穿你的牌了',
  '再见，这把我赢定了',
  '小心我的同花顺！',
];

// 更新时间戳
let lastUpdateTime = Date.now();

// 初始化Nacos配置客户端
const configClient = new NacosConfigClient({
  serverAddr: '192.168.0.46:8848', // Nacos服务器地址
  namespace: '827f75fe-9ada-4f38-bccd-65107a8a08d9', // 命名空间ID
  // 如果需要认证
  // username: 'nacos',
  // password: 'nacos',
  requestTimeout: 6000 // 请求超时时间，单位毫秒
});

// 从nacos获取配置的函数
async function fetchConfigFromNacos() {
  try {
    // 使用SDK获取配置
    const content = await configClient.getConfig('poker-taunt-options', 'DEFAULT_GROUP');
    console.log('Nacos原始响应数据:', content);
    
    if (content) {
      try {
        // 解析配置数据
        const configData = JSON.parse(content);
        if (Array.isArray(configData)) {
          tauntOptionsCache = configData;
          lastUpdateTime = Date.now();
          console.log('嘲讽选项配置已更新:', tauntOptionsCache);
        }
      } catch (parseError) {
        console.error('解析配置数据失败:', parseError);
      }
    }
  } catch (error) {
    console.error('从nacos获取嘲讽选项配置失败:', error);
  }
}

// 监听配置变化
configClient.subscribe({
  dataId: 'poker-taunt-options',
  group: 'DEFAULT_GROUP'
}, (content: string) => {
  console.log('配置已更新，新内容:', content);
  try {
    const configData = JSON.parse(content);
    if (Array.isArray(configData)) {
      tauntOptionsCache = configData;
      lastUpdateTime = Date.now();
      console.log('嘲讽选项配置已自动更新:', tauntOptionsCache);
    }
  } catch (error) {
    console.error('解析更新的配置数据失败:', error);
  }
});

// 初始化时获取一次配置
fetchConfigFromNacos().catch(err => console.error('初始化获取配置失败:', err));

const router = new Router({
  prefix: '/api/config',
});

// 获取嘲讽选项的API
router.get('/taunt-options', (ctx) => {
  ctx.body = tauntOptionsCache;
});

export const configRouter = router; 