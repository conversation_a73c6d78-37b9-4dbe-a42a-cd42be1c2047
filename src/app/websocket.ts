import { message } from "antd";
import { ActionType } from "../ApiType";
import { setChipsRecord } from "../features/chipsrecord/chipsRecordSlice";
import { clearCreateRoomID } from "../features/createroom/createRoomSlice";
import { addLogs } from "../features/gamehistory/gameHistorySlice";
import { roominfo } from "../features/home/<USER>";
import { clearRoomID } from "../features/home/<USER>";
import {
  setGame,
  setHands,
  setRoom,
  setSelectSettleTimes,
  setSelf,
  setUser,
} from "../features/room/roomSlice";
import { AppThunk } from "./store";
import { store } from "./store";

// 修改WebSocket服务器配置
// WebSocket服务器配置
// 根据package.json中的proxy配置，WebSocket服务器运行在8086端口
const WS_PATH = "/"; // WebSocket路径，根据服务器配置可能需要调整
const WS_PORT = "8086"; // 从package.json的proxy字段获取

let ws: WebSocket;
let pendingMessages: any[] = [];
let isConnecting = false;
let connectionAttempts = 0; // 记录连接尝试次数

// 添加连接状态监控
function getWebSocketStateString(state: number): string {
  switch (state) {
    case WebSocket.CONNECTING: return "正在连接";
    case WebSocket.OPEN: return "已连接";
    case WebSocket.CLOSING: return "正在关闭";
    case WebSocket.CLOSED: return "已关闭";
    default: return "未知状态";
  }
}

function send2server(data: any) {
  // 记录当前WebSocket状态
  const wsState = ws ? getWebSocketStateString(ws.readyState) : "不存在";
  console.log(`发送消息时WebSocket状态: ${wsState}`);

  if (!ws || ws.readyState !== WebSocket.OPEN) {
    // 如果连接不是OPEN状态，将消息加入队列等待连接建立后发送
    pendingMessages.push(data);
    console.log("WebSocket未连接，消息已加入队列:", data);

    // 如果WebSocket不存在或已关闭且没有正在连接，尝试重新连接
    if ((!ws || ws.readyState === WebSocket.CLOSED) && !isConnecting) {
      console.log("尝试重新建立WebSocket连接...");
      const roomId = localStorage.getItem("roomid");
      if (roomId) {
        console.log(`使用储存的房间ID重新连接: ${roomId}`);
        // 修复：传递第三个参数(unknown)为null或undefined
        const connectFn = connect2server(roomId);
        connectFn(store.dispatch, store.getState, undefined);
      }
    }
    return;
  }
  try {
    ws.send(JSON.stringify(data));
    console.log("消息已发送:", data);
  } catch (error) {
    console.error("发送消息失败:", error);
    // 发送失败时加入队列
    pendingMessages.push(data);
  }
}

// 发送所有排队中的消息
function sendPendingMessages() {
  if (pendingMessages.length > 0 && ws && ws.readyState === WebSocket.OPEN) {
    console.log(`正在发送${pendingMessages.length}条排队消息...`);
    // 创建临时数组并清空原数组，避免发送过程中新加入的消息被清空
    const messagesToSend = [...pendingMessages];
    pendingMessages = [];

    messagesToSend.forEach(data => {
      try {
        ws.send(JSON.stringify(data));
        console.log("队列消息已发送:", data);
      } catch (error) {
        console.error("发送排队消息失败:", error);
        // 发送失败的消息重新加入队列
        pendingMessages.push(data);
      }
    });

    if (pendingMessages.length > 0) {
      console.log(`还有${pendingMessages.length}条消息发送失败，已重新加入队列`);
    }
  }
}

export const connect2server =
  (roomid: string): AppThunk =>
    (dispatch, getState) => {
      if (ws && ws.readyState === WebSocket.OPEN) {
        console.log("WebSocket已连接，直接发送ENTER_GAME消息");
        send2server({ action: ActionType.ENTER_GAME, roomid });
        return;
      }

      // 如果正在连接中，避免重复连接
      if (isConnecting) {
        console.log("WebSocket正在连接中，请稍候...");
        pendingMessages.push({ action: ActionType.ENTER_GAME, roomid });
        return;
      }

      isConnecting = true;
      connectionAttempts++;
      // 保存roomid到localStorage，用于重连
      localStorage.setItem("roomid", roomid);

      // 构建WebSocket URL，使用固定的端口号
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.hostname;

      // 直接使用配置的端口号和路径
      const url = protocol === 'wss:'
        ? `${protocol}//${host}${WS_PATH}`
        : `${protocol}//${host}:${WS_PORT}${WS_PATH}`;

      console.log(`尝试WebSocket连接(第${connectionAttempts}次) URL:`, url);
      console.log("浏览器当前位置:", window.location.href);
      const token = localStorage["token"];

      try {
        ws = new WebSocket(url, token);
        console.log("WebSocket对象已创建，等待连接...");
      } catch (error) {
        console.error("创建WebSocket对象失败:", error);
        isConnecting = false;
        return;
      }

      let hideMessage: () => void = () => { };
      let connectTimer: ReturnType<typeof setTimeout>;
      let retrys = 0;

      function doConnect() {
        clearTimeout(connectTimer);
        if (++retrys < 10) {
          connectTimer = setTimeout(doConnect, 3000);
        }
        try {
          ws = new WebSocket(url, token);
          console.log(`正在重试连接(${retrys}/10)...`);
        } catch (error) {
          console.error(`WebSocket连接重试(${retrys}/10)失败:`, error);
        }
      }

      // 设置连接超时
      const connectionTimeout = setTimeout(() => {
        if (ws.readyState !== WebSocket.OPEN) {
          console.log("WebSocket连接超时");
          if (ws.readyState === WebSocket.CONNECTING) {
            ws.close();
          }
        }
      }, 5000);

      ws.onopen = () => {
        console.log("WebSocket连接已建立成功");
        clearTimeout(connectionTimeout);
        isConnecting = false;
        clearTimeout(connectTimer);
        retrys = 0;
        connectionAttempts = 0;

        // 连接成功后，先发送进入游戏请求
        send2server({ action: ActionType.ENTER_GAME, roomid });
        // 然后发送所有排队的消息
        sendPendingMessages();
        hideMessage();
      };
      ws.onmessage = (msg: MessageEvent) => {
        try {
          const data = JSON.parse(msg.data);
          if (data.code == -1) {
            message.error(data.error);
          }
          if (data.room) {
            dispatch(setRoom(data.room));
          }
          if (data.game) {
            dispatch(setGame(data.game));
          }
          if (data.self) {
            dispatch(setSelf(data.self));
          }
          if (data.user) {
            dispatch(setUser(data.user));
          }
          if (data.hands) {
            dispatch(setHands(data.hands));
          }
          if (data.chips) {
            dispatch(setChipsRecord(data.chips));
          }
          if (data.leave) {
            dispatch(clearRoomID(""));
            dispatch(clearCreateRoomID(""));
          }
          if (data.logs) {
            dispatch(addLogs(data.logs));
          }
          if (data.selectSettleTimes === 1) {
            dispatch(setSelectSettleTimes(true));
          }
          if (data.selectSettleTimes === 0) {
            dispatch(setSelectSettleTimes(false));
          }
          // 处理喊话消息
          if (data.tauntMessage) {
            const { sender, message, type, target } = data.tauntMessage;
            console.log("收到喊话消息:", data.tauntMessage);
            
            // 触发统一的喊话事件，所有角色都会收到，但只有发送者角色显示弹窗
            const tauntEvent = new CustomEvent('globalTaunt', {
              detail: {
                sender,
                message,
                type,
                ...(target && { target })
              }
            });
            window.dispatchEvent(tauntEvent);
          }

          console.log("收到WebSocket消息:", data);
        } catch (e) {
          console.log("解析WebSocket消息失败:", msg.data);
          console.error(e);
        }
      };
      ws.onclose = (event) => {
        console.log("WebSocket连接已关闭", event);
        isConnecting = false;
        if (hideMessage) {
          hideMessage();
        }
        hideMessage = message.loading(
          "网络链接不稳定，自动重试中。或者刷新页面。",
          0
        );
        setTimeout(doConnect, 1000);
      };

      ws.onerror = (event) => {
        console.error("WebSocket连接发生错误", event);
        isConnecting = false;
      };
    };

export function ws_startGame() {
  send2server({
    action: ActionType.START_GAME,
  });
}

export function ws_pauseGame() {
  send2server({
    action: ActionType.PAUSE_GAME,
  });
}

export function ws_userReady() {
  send2server({
    action: ActionType.READY,
  });
}

export function ws_userHangup() {
  send2server({
    action: ActionType.HANGUP,
  });
}

export function ws_userFold() {
  send2server({
    action: ActionType.FOLD,
  });
}

export function ws_userBet(chips: number) {
  send2server({
    action: ActionType.BET,
    chips,
  });
}

export function ws_userRebuy() {
  send2server({
    action: ActionType.REBUY,
  });
}

export function ws_userLeave() {
  send2server({
    action: ActionType.LEAVE,
  });
}

export function ws_overtime() {
  send2server({
    action: ActionType.OVERTIME,
  });
}

export function ws_userShowHands(index: number) {
  send2server({
    action: ActionType.SHOW_HANDS,
    index,
  });
}

export function ws_userWatch(watch: boolean) {
  send2server({
    action: ActionType.WATCH,
    watch,
  });
}

export function ws_settleTimes(times: number) {
  send2server({
    action: ActionType.SET_SETTLE_TIMES,
    times,
  });
}

export function ws_sendMessage(message: string) {
  send2server({
    action: ActionType.SEND_MESSAGE,
    message,
  });
  // 不再需要直接触发事件，将通过WebSocket接收服务器发送的事件
}
