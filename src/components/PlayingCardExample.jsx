import React from 'react';
import { PlayingCard, ALL_POINTS, ALL_SUITS } from './index';

const PlayingCardExample = () => {
  const [size, setSize] = React.useState('normal');

  return (
    <div className="card" style={{ padding: '20px' }}>
      <h2>扑克牌组件示例</h2>
      
      <div className="size-control" style={{ marginBottom: '20px' }}>
        <button onClick={() => setSize('small')} className={size === 'small' ? 'active' : ''}>
          小
        </button>
        <button onClick={() => setSize('normal')} className={size === 'normal' ? 'active' : ''}>
          中
        </button>
        <button onClick={() => setSize('big')} className={size === 'big' ? 'active' : ''}>
          大
        </button>
      </div>
      
      <div className="card-examples">
        <h3>花色示例</h3>
        <div>
          {ALL_SUITS.map(suit => (
            <PlayingCard key={suit} size={size} suit={suit} point="A" />
          ))}
        </div>
        
        <h3>点数示例</h3>
        <div>
          {ALL_POINTS.map(point => (
            <PlayingCard key={point} size={size} suit="♠" point={point} />
          ))}
        </div>
        
        <h3>所有卡片</h3>
        <div>
          {ALL_SUITS.map(suit => (
            ALL_POINTS.map(point => (
              <PlayingCard key={`${suit}-${point}`} size={size} suit={suit} point={point} />
            ))
          ))}
        </div>
      </div>
    </div>
  );
};

export default PlayingCardExample; 