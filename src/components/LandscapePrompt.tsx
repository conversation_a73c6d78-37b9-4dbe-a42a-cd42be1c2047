import React, { useEffect, useState } from 'react';
import './LandscapePrompt.css';

const LandscapePrompt: React.FC = () => {
  const [showPrompt, setShowPrompt] = useState(false);

  useEffect(() => {
    const checkOrientation = () => {
      const isMobile = window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      const isPortrait = window.innerHeight > window.innerWidth;
      
      if (isMobile && isPortrait) {
        setShowPrompt(true);
        // 尝试强制横屏（某些浏览器支持）
        if (window.screen && window.screen.orientation && window.screen.orientation.lock) {
          try {
            window.screen.orientation.lock('landscape').catch(() => {
              // 如果无法强制横屏，就显示提示
            });
          } catch (error) {
            // 浏览器不支持强制横屏
          }
        }
      } else {
        setShowPrompt(false);
      }
    };

    checkOrientation();
    window.addEventListener('resize', checkOrientation);
    window.addEventListener('orientationchange', checkOrientation);

    return () => {
      window.removeEventListener('resize', checkOrientation);
      window.removeEventListener('orientationchange', checkOrientation);
    };
  }, []);

  if (!showPrompt) return null;

  return (
    <div className="landscape-prompt-overlay">
      <div className="landscape-prompt-content">
        <div className="rotate-icon">
          📱 ➡️ 📱
        </div>
        <h2>请旋转设备</h2>
        <p>为获得更好的游戏体验，请将设备横向放置</p>
        <div className="device-rotate-animation">
          <div className="device"></div>
        </div>
      </div>
    </div>
  );
};

export default LandscapePrompt; 