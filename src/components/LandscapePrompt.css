.landscape-prompt-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  color: white;
}

.landscape-prompt-content {
  text-align: center;
  padding: 40px 20px;
  max-width: 90%;
}

.rotate-icon {
  font-size: 48px;
  margin-bottom: 20px;
  animation: pulse 2s infinite;
}

.landscape-prompt-content h2 {
  font-size: 28px;
  margin: 20px 0 10px 0;
  color: white;
}

.landscape-prompt-content p {
  font-size: 16px;
  margin: 10px 0 30px 0;
  color: #ccc;
  line-height: 1.5;
}

.device-rotate-animation {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
}

.device {
  width: 60px;
  height: 100px;
  border: 3px solid white;
  border-radius: 8px;
  position: relative;
  animation: rotateDevice 3s infinite;
}

.device::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 4px;
  background: white;
  border-radius: 2px;
}

.device::after {
  content: '';
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  border: 2px solid white;
  border-radius: 50%;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes rotateDevice {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(90deg);
  }
  75% {
    transform: rotate(90deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

/* 当设备已经是横屏时隐藏提示 */
@media (orientation: landscape) {
  .landscape-prompt-overlay {
    display: none;
  }
} 