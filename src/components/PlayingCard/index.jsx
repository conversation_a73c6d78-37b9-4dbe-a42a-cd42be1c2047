import React from 'react';
import PropTypes from 'prop-types';
import './styles.css';
import { ALL_POINTS, ALL_SUITS, SUIT_SYMBOLS } from './constants';

const PlayingCard = ({ size, suit, point, className, raised, flipped, backColor }) => {
  const isRed = suit === "♥" || suit === "♦";
  const suitSymbol = SUIT_SYMBOLS[suit] || suit;
  
  return (
    <div className={`playing-card ${size} ${isRed ? 'red' : ''} ${raised ? 'raised' : ''} ${flipped ? 'flipped' : ''} ${className || ''}`}>
      <div className="playing-card-inner">
        <div className="playing-card-front">
          <div className="playing-card-corner top-left">
            <div className="playing-card-point">{point}</div>
            <div className="playing-card-suit">{suitSymbol}</div>
          </div>
          <div className="playing-card-center">
            <div className="playing-card-symbol center-symbol">{suitSymbol}</div>
          </div>
          <div className="playing-card-corner bottom-right">
            <div className="playing-card-point">{point}</div>
            <div className="playing-card-suit">{suitSymbol}</div>
          </div>
        </div>
        <div className={`playing-card-back ${backColor === 'red' ? 'red-back' : ''}`}>
          <div className={`playing-card-back-pattern ${backColor === 'red' ? 'red-pattern' : ''}`}></div>
        </div>
      </div>
    </div>
  );
};

PlayingCard.propTypes = {
  size: PropTypes.oneOf(['small', 'normal', 'big']),
  suit: PropTypes.oneOf(ALL_SUITS),
  point: PropTypes.oneOf(ALL_POINTS),
  className: PropTypes.string,
  raised: PropTypes.bool,
  flipped: PropTypes.bool,
  backColor: PropTypes.oneOf(['blue', 'red']),
};

PlayingCard.defaultProps = {
  size: 'normal',
  className: '',
  raised: false,
  flipped: false,
  backColor: 'blue',
};

export default PlayingCard; 