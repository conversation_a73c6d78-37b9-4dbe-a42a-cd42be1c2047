/* PlayingCard 组件样式 */
.playing-card {
  display: inline-block;
  border-radius: 6px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  position: relative;
  user-select: none;
  margin: 5px;
  color: black;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  perspective: 1000px; /* 3D效果的透视距离 */
}

/* 上移效果 */
.playing-card.raised {
  transform: translateY(-15px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

/* 卡片尺寸 */
.playing-card.small {
  width: 55px;
  height: 80px;
  font-size: 12px;
}

.playing-card.normal {
  width: 80px;
  height: 120px;
  font-size: 16px;
}

.playing-card.big {
  width: 110px;
  height: 160px;
  font-size: 22px;
}

/* 红色花色 */
.playing-card.red {
  color: #e44145;
}

/* 内部容器 */
.playing-card-inner {
  width: 100%;
  height: 100%;
  position: relative;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

/* 翻转效果 */
.playing-card.flipped .playing-card-inner {
  transform: rotateY(180deg);
}

/* 正面和背面共享样式 */
.playing-card-front, .playing-card-back {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  position: absolute;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden; /* Safari兼容 */
}

/* 正面 */
.playing-card-front {
  background-color: white;
  z-index: 2;
}

/* 背面 */
.playing-card-back {
  background-color: #2563eb; /* 深蓝色背景 */
  transform: rotateY(180deg);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

/* 红色卡背样式 */
.playing-card-back.red-back {
  background-color: #e11d48; /* 深红色背景 */
}

/* 卡牌背面花纹 */
.playing-card-back-pattern {
  width: 90%;
  height: 90%;
  background: repeating-linear-gradient(
    45deg,
    #3b82f6,
    #3b82f6 10px,
    #1d4ed8 10px,
    #1d4ed8 20px
  );
  border-radius: 4px;
  position: relative;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
}

/* 红色卡背花纹 */
.playing-card-back-pattern.red-pattern {
  background: repeating-linear-gradient(
    45deg,
    #f87171,
    #f87171 10px,
    #dc2626 10px,
    #dc2626 20px
  );
}

.playing-card-back-pattern:before {
  content: "♠♣♥♦";
  font-size: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
}

/* 不同尺寸卡片的背面图案尺寸调整 */
.playing-card.small .playing-card-back-pattern:before {
  font-size: 12px;
}

.playing-card.normal .playing-card-back-pattern:before {
  font-size: 16px;
}

.playing-card.big .playing-card-back-pattern:before {
  font-size: 24px;
}

/* ============== 移动端扑克牌适配 ============== */
@media (max-width: 768px) {
  /* 移动端扑克牌基础样式 */
  .playing-card {
    margin: 2px;
  }
  
  .playing-card.small {
    width: 45px;
    height: 65px;
    font-size: 10px;
  }

  .playing-card.normal {
    width: 55px;
    height: 80px;
    font-size: 12px;
  }

  .playing-card.big {
    width: 70px;
    height: 100px;
    font-size: 16px;
  }
  
  /* 移动端扑克牌角落信息 */
  .playing-card.small .playing-card-point {
    font-size: 10px;
  }

  .playing-card.normal .playing-card-point {
    font-size: 14px;
  }

  .playing-card.big .playing-card-point {
    font-size: 18px;
  }
  
  .playing-card.small .playing-card-suit {
    font-size: 8px;
  }

  .playing-card.normal .playing-card-suit {
    font-size: 12px;
  }

  .playing-card.big .playing-card-suit {
    font-size: 16px;
  }
  
  /* 移动端扑克牌中心花色 */
  .playing-card.small .playing-card-symbol {
    font-size: 10px;
  }

  .playing-card.normal .playing-card-symbol {
    font-size: 14px;
  }

  .playing-card.big .playing-card-symbol {
    font-size: 18px;
  }
  
  .playing-card.small .playing-card-symbol.center-symbol {
    font-size: 18px;
  }

  .playing-card.normal .playing-card-symbol.center-symbol {
    font-size: 24px;
  }

  .playing-card.big .playing-card-symbol.center-symbol {
    font-size: 32px;
  }
}

/* 小屏幕手机进一步缩小 */
@media (max-width: 480px) {
  .playing-card.small {
    width: 40px;
    height: 58px;
    font-size: 9px;
  }

  .playing-card.normal {
    width: 48px;
    height: 70px;
    font-size: 11px;
  }

  .playing-card.big {
    width: 60px;
    height: 88px;
    font-size: 14px;
  }
}

/* 角落花色和点数 */
.playing-card-corner {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 1;
  padding: 4px;
}

.playing-card-corner.top-left {
  top: 0;
  left: 0;
}

.playing-card-corner.bottom-right {
  bottom: 0;
  right: 0;
  transform: rotate(180deg);
}

/* 点数 */
.playing-card-point {
  font-weight: bold;
  font-size: 120%;
}

.playing-card.small .playing-card-point {
  font-size: 14px;
}

.playing-card.normal .playing-card-point {
  font-size: 18px;
}

.playing-card.big .playing-card-point {
  font-size: 24px;
}

/* 花色 */
.playing-card-suit {
  font-size: 100%;
}

.playing-card.small .playing-card-suit {
  font-size: 12px;
}

.playing-card.normal .playing-card-suit {
  font-size: 16px;
}

.playing-card.big .playing-card-suit {
  font-size: 20px;
}

/* 中心区域 */
.playing-card-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  height: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 花牌 */
.playing-card-face {
  font-size: 200%;
  font-weight: bold;
}

/* A牌 - 修复居中问题 */
.playing-card-symbol.ace {
  font-size: 350%;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 数字牌花色 */
.playing-card-symbols {
  position: relative;
  width: 100%;
  height: 100%;
}

.playing-card-symbol {
  position: absolute;
  transform: translate(-50%, -50%);
  font-size: 120%;
}

.playing-card.big .playing-card-symbol {
  font-size: 24px;
}

.playing-card.normal .playing-card-symbol {
  font-size: 18px;
}

.playing-card.small .playing-card-symbol {
  font-size: 14px;
}

/* 不同牌面数字的花色位置 */
/* 2张牌 */
.playing-card-symbol.pos-1-2 {
  top: 30%;
  left: 50%;
}

.playing-card-symbol.pos-2-2 {
  top: 70%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(180deg);
}

/* 3张牌 */
.playing-card-symbol.pos-1-3 {
  top: 20%;
  left: 50%;
}

.playing-card-symbol.pos-2-3 {
  top: 50%;
  left: 50%;
}

.playing-card-symbol.pos-3-3 {
  top: 80%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(180deg);
}

/* 4张牌 */
.playing-card-symbol.pos-1-4 {
  top: 25%;
  left: 25%;
}

.playing-card-symbol.pos-2-4 {
  top: 25%;
  left: 75%;
}

.playing-card-symbol.pos-3-4 {
  top: 75%;
  left: 25%;
  transform: translate(-50%, -50%) rotate(180deg);
}

.playing-card-symbol.pos-4-4 {
  top: 75%;
  left: 75%;
  transform: translate(-50%, -50%) rotate(180deg);
}

/* 5张牌 */
.playing-card-symbol.pos-1-5 {
  top: 20%;
  left: 25%;
}

.playing-card-symbol.pos-2-5 {
  top: 20%;
  left: 75%;
}

.playing-card-symbol.pos-3-5 {
  top: 50%;
  left: 50%;
}

.playing-card-symbol.pos-4-5 {
  top: 80%;
  left: 25%;
  transform: translate(-50%, -50%) rotate(180deg);
}

.playing-card-symbol.pos-5-5 {
  top: 80%;
  left: 75%;
  transform: translate(-50%, -50%) rotate(180deg);
}

/* 6张牌 */
.playing-card-symbol.pos-1-6 {
  top: 15%;
  left: 25%;
}

.playing-card-symbol.pos-2-6 {
  top: 15%;
  left: 75%;
}

.playing-card-symbol.pos-3-6 {
  top: 50%;
  left: 25%;
}

.playing-card-symbol.pos-4-6 {
  top: 50%;
  left: 75%;
}

.playing-card-symbol.pos-5-6 {
  top: 85%;
  left: 25%;
  transform: translate(-50%, -50%) rotate(180deg);
}

.playing-card-symbol.pos-6-6 {
  top: 85%;
  left: 75%;
  transform: translate(-50%, -50%) rotate(180deg);
}

/* 7张牌 */
.playing-card-symbol.pos-1-7 {
  top: 15%;
  left: 25%;
}

.playing-card-symbol.pos-2-7 {
  top: 15%;
  left: 75%;
}

.playing-card-symbol.pos-3-7 {
  top: 37%;
  left: 50%;
}

.playing-card-symbol.pos-4-7 {
  top: 50%;
  left: 25%;
}

.playing-card-symbol.pos-5-7 {
  top: 50%;
  left: 75%;
}

.playing-card-symbol.pos-6-7 {
  top: 85%;
  left: 25%;
  transform: translate(-50%, -50%) rotate(180deg);
}

.playing-card-symbol.pos-7-7 {
  top: 85%;
  left: 75%;
  transform: translate(-50%, -50%) rotate(180deg);
}

/* 8张牌 */
.playing-card-symbol.pos-1-8 {
  top: 15%;
  left: 25%;
}

.playing-card-symbol.pos-2-8 {
  top: 15%;
  left: 75%;
}

.playing-card-symbol.pos-3-8 {
  top: 37%;
  left: 25%;
}

.playing-card-symbol.pos-4-8 {
  top: 37%;
  left: 75%;
}

.playing-card-symbol.pos-5-8 {
  top: 63%;
  left: 25%;
  transform: translate(-50%, -50%) rotate(180deg);
}

.playing-card-symbol.pos-6-8 {
  top: 63%;
  left: 75%;
  transform: translate(-50%, -50%) rotate(180deg);
}

.playing-card-symbol.pos-7-8 {
  top: 85%;
  left: 25%;
  transform: translate(-50%, -50%) rotate(180deg);
}

.playing-card-symbol.pos-8-8 {
  top: 85%;
  left: 75%;
  transform: translate(-50%, -50%) rotate(180deg);
}

/* 9张牌 - 修复重叠问题 */
.playing-card-symbol.pos-1-9 {
  top: 12%;
  left: 25%;
}

.playing-card-symbol.pos-2-9 {
  top: 12%;
  left: 75%;
}

.playing-card-symbol.pos-3-9 {
  top: 32%;
  left: 25%;
}

.playing-card-symbol.pos-4-9 {
  top: 32%;
  left: 75%;
}

.playing-card-symbol.pos-5-9 {
  top: 50%;
  left: 50%;
}

.playing-card-symbol.pos-6-9 {
  top: 68%;
  left: 25%;
  transform: translate(-50%, -50%) rotate(180deg);
}

.playing-card-symbol.pos-7-9 {
  top: 68%;
  left: 75%;
  transform: translate(-50%, -50%) rotate(180deg);
}

.playing-card-symbol.pos-8-9 {
  top: 88%;
  left: 25%;
  transform: translate(-50%, -50%) rotate(180deg);
}

.playing-card-symbol.pos-9-9 {
  top: 88%;
  left: 75%;
  transform: translate(-50%, -50%) rotate(180deg);
}

/* 10张牌 - 完全重写 */
.playing-card-symbol.pos-1-10 {
  top: 10%;
  left: 25%;
}

.playing-card-symbol.pos-2-10 {
  top: 10%;
  left: 75%;
}

.playing-card-symbol.pos-3-10 {
  top: 30%;
  left: 25%;
}

.playing-card-symbol.pos-4-10 {
  top: 30%;
  left: 75%;
}

.playing-card-symbol.pos-5-10 {
  top: 50%;
  left: 25%;
}

.playing-card-symbol.pos-6-10 {
  top: 50%;
  left: 75%;
}

.playing-card-symbol.pos-7-10 {
  top: 70%;
  left: 25%;
  transform: translate(-50%, -50%) rotate(180deg);
}

.playing-card-symbol.pos-8-10 {
  top: 70%;
  left: 75%;
  transform: translate(-50%, -50%) rotate(180deg);
}

.playing-card-symbol.pos-9-10 {
  top: 90%;
  left: 25%;
  transform: translate(-50%, -50%) rotate(180deg);
}

.playing-card-symbol.pos-10-10 {
  top: 90%;
  left: 75%;
  transform: translate(-50%, -50%) rotate(180deg);
}

/* A牌使用大字母显示 */
.playing-card-face.ace-letter {
  font-size: 350%;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 中心花色 */
.playing-card-symbol.center-symbol {
  position: static;
  transform: none;
  font-size: 150%;
}

.playing-card.big .playing-card-symbol.center-symbol {
  font-size: 200%;
}

.playing-card.normal .playing-card-symbol.center-symbol {
  font-size: 180%;
}

.playing-card.small .playing-card-symbol.center-symbol {
  font-size: 150%;
} 