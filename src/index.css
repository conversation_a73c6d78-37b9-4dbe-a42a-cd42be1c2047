body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 移动端优化 */
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 移动端全局优化 */
@media (max-width: 768px) {
  html, body {
    /* 防止过度滚动 */
    overscroll-behavior: none;
    /* 防止双击缩放 */
    touch-action: manipulation;
    /* 优化滚动性能 */
    -webkit-overflow-scrolling: touch;
  }
  
  /* 防止iOS Safari的橡皮筋效果 */
  body {
    position: fixed;
    width: 100%;
    height: 100%;
  }
}
