import { Card } from "../ApiType";
import crypto from 'crypto';

const colors = require("colors");
export const NUMS = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14];
export const SUITS = ["c", "d", "h", "s"]; // club ♣︎, diamond ♦︎, heart ♥︎, spade ♠︎

/**
 * 加密安全的随机数生成器
 * 使用Node.js crypto模块生成高质量随机数
 */
function secureRandom(): number {
  const buffer = crypto.randomBytes(4);
  return buffer.readUInt32BE(0) / 0x100000000;
}

/**
 * 生成指定范围内的安全随机整数
 * @param max 最大值（不包含）
 * @returns 0到max-1之间的随机整数
 */
function secureRandomInt(max: number): number {
  if (max <= 0) return 0;
  return Math.floor(secureRandom() * max);
}

/**
 * 改进的Fisher-Yates洗牌算法
 * 使用加密安全的随机数生成器
 * @param deck 要洗牌的牌组
 */
function improvedFisherYatesShuffle(deck: Card[]): void {
  for (let i = deck.length - 1; i > 0; i--) {
    const j = secureRandomInt(i + 1);
    [deck[i], deck[j]] = [deck[j], deck[i]];
  }
}

/**
 * 多重洗牌 - 增加随机性
 * @param deck 要洗牌的牌组
 * @param rounds 洗牌轮数，默认3轮
 */
function multipleShuffles(deck: Card[], rounds: number = 3): void {
  for (let round = 0; round < rounds; round++) {
    improvedFisherYatesShuffle(deck);
  }
}

/**
 * 验证洗牌质量
 * 检查洗牌后的牌组是否具有良好的随机性
 * @param deck 洗牌后的牌组
 * @returns 质量评分 (0-1，1为最佳)
 */
function validateShuffleQuality(deck: Card[]): number {
  if (deck.length < 4) return 1; // 太短无法验证
  
  let score = 1.0;
  
  // 检查相邻牌的花色和数字分布
  let sameSuitAdjacent = 0;
  let sameNumberAdjacent = 0;
  let consecutiveNumbers = 0;
  
  for (let i = 0; i < deck.length - 1; i++) {
    const current = deck[i];
    const next = deck[i + 1];
    
    if (current.suit === next.suit) {
      sameSuitAdjacent++;
    }
    
    if (current.num === next.num) {
      sameNumberAdjacent++;
    }
    
    if (Math.abs(current.num - next.num) === 1) {
      consecutiveNumbers++;
    }
  }
  
  // 计算期望值
  const expectedSameSuit = (deck.length - 1) / 4; // 25%概率
  const expectedSameNumber = (deck.length - 1) / 13; // ~7.7%概率
  const expectedConsecutive = (deck.length - 1) * 2 / 13; // ~15.4%概率
  
  // 计算偏差并调整分数
  const suitDeviation = Math.abs(sameSuitAdjacent - expectedSameSuit) / expectedSameSuit;
  const numberDeviation = Math.abs(sameNumberAdjacent - expectedSameNumber) / expectedSameNumber;
  const consecutiveDeviation = Math.abs(consecutiveNumbers - expectedConsecutive) / expectedConsecutive;
  
  score -= Math.min(0.3, suitDeviation * 0.1);
  score -= Math.min(0.3, numberDeviation * 0.1);
  score -= Math.min(0.3, consecutiveDeviation * 0.1);
  
  return Math.max(0, score);
}

/**
 * 改进的随机发牌函数
 * @param len 需要的牌数
 * @param ensureQuality 是否确保洗牌质量，默认true
 * @returns 洗牌后的牌组
 */
export function improvedRandomHands(len = 7, ensureQuality = true): Card[] {
  const deck: Card[] = [];
  
  // 创建完整牌组
  for (const suit of SUITS) {
    for (const num of NUMS) {
      deck.push({ num, suit });
    }
  }
  
  let shuffleAttempts = 0;
  const maxAttempts = 10;
  
  do {
    // 执行多重洗牌
    multipleShuffles(deck, 3);
    shuffleAttempts++;
    
    // 如果不需要质量检查或质量足够好，则退出循环
    if (!ensureQuality || validateShuffleQuality(deck) >= 0.7) {
      break;
    }
    
    // 如果尝试次数过多，记录警告但继续使用
    if (shuffleAttempts >= maxAttempts) {
      console.warn(`洗牌质量检查失败，已尝试${maxAttempts}次，使用当前结果`);
      break;
    }
  } while (shuffleAttempts < maxAttempts);
  
  const result = deck.slice(0, len);
  
  // 添加洗牌信息用于审计
  result.forEach(card => {
    (card as any).shuffleMethod = 'improved-fisher-yates';
    (card as any).shuffleRounds = 3;
    (card as any).shuffleAttempts = shuffleAttempts;
    (card as any).shuffleTimestamp = Date.now();
  });
  
  return result;
}

/**
 * 生成审计日志
 * @param cards 发牌结果
 * @param gameId 游戏ID
 * @param players 玩家列表
 */
export function generateAuditLog(cards: Card[], gameId: string, players: string[]): void {
  const auditData = {
    gameId,
    timestamp: Date.now(),
    players: players.length,
    shuffleMethod: (cards[0] as any)?.shuffleMethod || 'unknown',
    shuffleRounds: (cards[0] as any)?.shuffleRounds || 0,
    shuffleAttempts: (cards[0] as any)?.shuffleAttempts || 0,
    qualityScore: validateShuffleQuality(cards),
    // 不记录具体牌面，只记录统计信息
    suitDistribution: SUITS.map(suit => 
      cards.filter(card => card.suit === suit).length
    ),
    numberDistribution: NUMS.map(num => 
      cards.filter(card => card.num === num).length
    )
  };
  
  // 在生产环境中，这里应该写入专门的审计日志文件
  console.log('发牌审计日志:', JSON.stringify(auditData, null, 2));
}

/**
 * 安全的随机选择函数
 * @param items 要选择的项目数组
 * @returns 随机选择的项目
 */
export function secureRandomPickOne<T>(items: T[]): T {
  if (items.length === 0) {
    throw new Error('Cannot pick from empty array');
  }
  return items[secureRandomInt(items.length)];
}

/**
 * 生成加密安全的游戏种子
 * @returns 32字节的十六进制种子字符串
 */
export function generateGameSeed(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * 基于种子的确定性随机数生成器（用于可重现的测试）
 * 注意：这个函数仅用于测试目的，不应在生产环境中用于发牌
 */
export class SeededRandom {
  private seed: number;
  
  constructor(seed: string | number) {
    this.seed = typeof seed === 'string' ? this.hashCode(seed) : seed;
  }
  
  private hashCode(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }
  
  next(): number {
    this.seed = (this.seed * 9301 + 49297) % 233280;
    return this.seed / 233280;
  }
  
  nextInt(max: number): number {
    return Math.floor(this.next() * max);
  }
}

/**
 * 用于测试的确定性洗牌函数
 * @param deck 要洗牌的牌组
 * @param seed 随机种子
 */
export function deterministicShuffle(deck: Card[], seed: string): void {
  const rng = new SeededRandom(seed);
  
  for (let i = deck.length - 1; i > 0; i--) {
    const j = rng.nextInt(i + 1);
    [deck[i], deck[j]] = [deck[j], deck[i]];
  }
}

// 导出原有函数以保持兼容性
export function randomHands(len = 7): Card[] {
  return improvedRandomHands(len, true);
}

export function _randomPickOne(items: any) {
  return secureRandomPickOne(items);
}
