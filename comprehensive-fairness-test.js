const { randomHands, rank, pokerTypeName, NUMS, SUITS } = require('./dist/src/server/utils/game-engine.js');

// 1. 测试随机数生成器的质量
function testRandomnessQuality(samples = 100000) {
  console.log('=== 随机数生成器质量测试 ===');
  
  const values = [];
  for (let i = 0; i < samples; i++) {
    values.push(Math.random());
  }
  
  // 均匀性测试 - 卡方检验
  const bins = 10;
  const expected = samples / bins;
  const observed = new Array(bins).fill(0);
  
  values.forEach(v => {
    const bin = Math.floor(v * bins);
    observed[bin]++;
  });
  
  let chiSquare = 0;
  for (let i = 0; i < bins; i++) {
    chiSquare += Math.pow(observed[i] - expected, 2) / expected;
  }
  
  console.log(`卡方统计量: ${chiSquare.toFixed(3)} (自由度: ${bins-1})`);
  console.log(`临界值(α=0.05): 16.919`);
  console.log(`均匀性测试: ${chiSquare < 16.919 ? '✅ 通过' : '❌ 失败'}`);
  
  // 独立性测试 - 游程检验
  let runs = 1;
  for (let i = 1; i < Math.min(1000, values.length); i++) {
    if ((values[i] > 0.5) !== (values[i-1] > 0.5)) {
      runs++;
    }
  }
  
  const n = Math.min(1000, values.length);
  const expectedRuns = (2 * n) / 3;
  const variance = (16 * n - 29) / 90;
  const z = Math.abs(runs - expectedRuns) / Math.sqrt(variance);
  
  console.log(`游程数: ${runs}, 期望: ${expectedRuns.toFixed(1)}`);
  console.log(`Z统计量: ${z.toFixed(3)} (临界值: 1.96)`);
  console.log(`独立性测试: ${z < 1.96 ? '✅ 通过' : '❌ 失败'}`);
}

// 2. 测试洗牌算法的公平性
function testShuffleAlgorithm(iterations = 50000) {
  console.log('\n=== 洗牌算法公平性测试 ===');
  
  const cardPositions = {};
  const deckSize = 52;
  
  // 初始化统计
  for (let card = 0; card < deckSize; card++) {
    cardPositions[card] = new Array(deckSize).fill(0);
  }
  
  for (let i = 0; i < iterations; i++) {
    const deck = randomHands(52);
    
    // 统计每张牌出现在每个位置的次数
    deck.forEach((card, position) => {
      const cardIndex = SUITS.indexOf(card.suit) * 13 + NUMS.indexOf(card.num);
      cardPositions[cardIndex][position]++;
    });
  }
  
  // 计算卡方统计量
  const expected = iterations / deckSize;
  let totalChiSquare = 0;
  let maxDeviation = 0;
  
  for (let card = 0; card < deckSize; card++) {
    let cardChiSquare = 0;
    for (let pos = 0; pos < deckSize; pos++) {
      const observed = cardPositions[card][pos];
      const deviation = Math.abs(observed - expected);
      maxDeviation = Math.max(maxDeviation, deviation);
      cardChiSquare += Math.pow(observed - expected, 2) / expected;
    }
    totalChiSquare += cardChiSquare;
  }
  
  console.log(`总卡方统计量: ${totalChiSquare.toFixed(3)}`);
  console.log(`最大偏差: ${maxDeviation.toFixed(1)} (期望: ${expected.toFixed(1)})`);
  console.log(`相对偏差: ${(maxDeviation/expected*100).toFixed(2)}%`);
  
  // 检查是否有明显偏差
  const acceptableDeviation = expected * 0.1; // 10%的偏差是可接受的
  console.log(`洗牌公平性: ${maxDeviation < acceptableDeviation ? '✅ 通过' : '❌ 失败'}`);
}

// 3. 测试入场顺序对发牌的影响
function testEntryOrderBias(rounds = 10000) {
  console.log('\n=== 入场顺序偏差测试 ===');
  
  const players = 6;
  const playerStats = Array(players).fill(0).map(() => ({
    totalValue: 0,
    pairCount: 0,
    premiumHands: 0 // AA, KK, QQ, AK
  }));
  
  for (let round = 0; round < rounds; round++) {
    const deck = randomHands(52);
    
    // 模拟按入场顺序发牌
    for (let p = 0; p < players; p++) {
      const hand1 = deck[p * 2];
      const hand2 = deck[p * 2 + 1];
      
      // 计算手牌价值
      const value1 = hand1.num === 14 ? 14 : hand1.num;
      const value2 = hand2.num === 14 ? 14 : hand2.num;
      const handValue = value1 + value2;
      
      playerStats[p].totalValue += handValue;
      
      // 检查对子
      if (value1 === value2) {
        playerStats[p].pairCount++;
      }
      
      // 检查优质起手牌
      const sortedValues = [value1, value2].sort((a, b) => b - a);
      if ((sortedValues[0] === 14 && sortedValues[1] === 14) || // AA
          (sortedValues[0] === 13 && sortedValues[1] === 13) || // KK
          (sortedValues[0] === 12 && sortedValues[1] === 12) || // QQ
          (sortedValues[0] === 14 && sortedValues[1] === 13)) { // AK
        playerStats[p].premiumHands++;
      }
    }
  }
  
  console.log('位置\t平均牌值\t对子率%\t优质牌率%');
  
  const avgValues = [];
  const pairRates = [];
  const premiumRates = [];
  
  for (let p = 0; p < players; p++) {
    const avgValue = playerStats[p].totalValue / rounds;
    const pairRate = playerStats[p].pairCount / rounds * 100;
    const premiumRate = playerStats[p].premiumHands / rounds * 100;
    
    avgValues.push(avgValue);
    pairRates.push(pairRate);
    premiumRates.push(premiumRate);
    
    console.log(`${p+1}\t${avgValue.toFixed(2)}\t\t${pairRate.toFixed(1)}\t${premiumRate.toFixed(1)}`);
  }
  
  // 计算方差
  const avgValueMean = avgValues.reduce((a, b) => a + b) / players;
  const pairRateMean = pairRates.reduce((a, b) => a + b) / players;
  const premiumRateMean = premiumRates.reduce((a, b) => a + b) / players;
  
  const valueVariance = avgValues.reduce((sum, val) => sum + Math.pow(val - avgValueMean, 2), 0) / players;
  const pairVariance = pairRates.reduce((sum, val) => sum + Math.pow(val - pairRateMean, 2), 0) / players;
  const premiumVariance = premiumRates.reduce((sum, val) => sum + Math.pow(val - premiumRateMean, 2), 0) / players;
  
  console.log(`\n方差分析:`);
  console.log(`平均牌值方差: ${valueVariance.toFixed(4)} ${valueVariance < 0.1 ? '✅' : '❌'}`);
  console.log(`对子率方差: ${pairVariance.toFixed(4)} ${pairVariance < 0.5 ? '✅' : '❌'}`);
  console.log(`优质牌率方差: ${premiumVariance.toFixed(4)} ${premiumVariance < 0.5 ? '✅' : '❌'}`);
}

// 4. 测试座位位置对获胜率的影响
function testSeatPositionWinRate(rounds = 5000) {
  console.log('\n=== 座位位置获胜率测试 ===');
  
  const players = 6;
  const playerWins = new Array(players).fill(0);
  const playerGames = new Array(players).fill(0);
  
  for (let round = 0; round < rounds; round++) {
    const deck = randomHands(52);
    const communityCards = deck.slice(players * 2, players * 2 + 5);
    
    const playerHands = [];
    for (let p = 0; p < players; p++) {
      const hand = [deck[p * 2], deck[p * 2 + 1], ...communityCards];
      const result = rank(hand);
      playerHands.push({ player: p, rank: result, cards: hand });
      playerGames[p]++;
    }
    
    // 找出获胜者
    playerHands.sort((a, b) => {
      // 使用游戏引擎的比较函数
      const comparison = rank(b.cards).type - rank(a.cards).type;
      if (comparison !== 0) return comparison;
      
      // 如果牌型相同，需要更详细的比较
      return 0; // 简化处理，认为平局
    });
    
    // 最高牌型的玩家获胜
    const winningType = playerHands[0].rank.type;
    const winners = playerHands.filter(p => p.rank.type === winningType);
    
    winners.forEach(winner => {
      playerWins[winner.player] += 1 / winners.length;
    });
  }
  
  console.log('位置\t获胜率%\t期望获胜率%\t偏差');
  const expectedWinRate = 100 / players;
  let maxDeviation = 0;
  
  for (let p = 0; p < players; p++) {
    const winRate = (playerWins[p] / rounds) * 100;
    const deviation = Math.abs(winRate - expectedWinRate);
    maxDeviation = Math.max(maxDeviation, deviation);
    
    console.log(`${p+1}\t${winRate.toFixed(2)}\t\t${expectedWinRate.toFixed(2)}\t\t${deviation.toFixed(2)}`);
  }
  
  console.log(`\n最大偏差: ${maxDeviation.toFixed(2)}%`);
  console.log(`座位公平性: ${maxDeviation < 2.0 ? '✅ 通过' : '❌ 失败'}`);
}

// 5. 综合公平性评估
function comprehensiveFairnessTest() {
  console.log('=== 德州扑克发牌算法公平性综合分析 ===\n');
  
  testRandomnessQuality();
  testShuffleAlgorithm();
  testEntryOrderBias();
  testSeatPositionWinRate();
  
  console.log('\n=== 分析总结 ===');
  console.log('1. 随机数生成器使用JavaScript内置Math.random()');
  console.log('2. 洗牌算法使用Fisher-Yates和Inside-Out随机选择');
  console.log('3. 发牌顺序按固定模式：每人依次发两张手牌');
  console.log('4. 座位分配基于入场顺序和大盲位轮换');
  console.log('\n建议查看上述测试结果判断是否存在公平性问题。');
}

// 运行综合测试
comprehensiveFairnessTest();
