/* 移动端优化样式 */

/* 1. 改进的触摸友好按钮 */
@media (max-width: 768px) {
  .ant-btn {
    min-height: 44px !important; /* Apple推荐的最小触摸目标 */
    font-size: 16px !important; /* 防止iOS缩放 */
    border-radius: 8px !important;
    font-weight: 500;
  }
  
  /* 主要操作按钮更大 */
  .user-actions .ant-btn-primary {
    min-height: 50px !important;
    font-size: 18px !important;
    font-weight: 600;
  }
}

/* 2. 改进的扑克牌显示 */
@media (max-width: 768px) {
  .poker-card {
    min-width: 60px !important;
    min-height: 84px !important; /* 保持扑克牌比例 */
    font-size: 14px !important;
    font-weight: bold;
    border: 2px solid #333;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  }
  
  /* 手牌更大显示 */
  .user-hands .poker-card {
    min-width: 70px !important;
    min-height: 98px !important;
    font-size: 16px !important;
  }
  
  /* 公共牌适中显示 */
  .boards .poker-card {
    min-width: 55px !important;
    min-height: 77px !important;
    font-size: 13px !important;
  }
}

/* 3. 改进的用户信息显示 */
@media (max-width: 768px) {
  .user {
    min-width: 80px !important;
    padding: 8px 4px !important;
    margin: 2px !important;
  }
  
  .user .ant-avatar {
    width: 40px !important;
    height: 40px !important;
    font-size: 16px !important;
  }
  
  .user-info {
    font-size: 12px !important;
    line-height: 1.2;
  }
  
  .user-chips {
    font-size: 13px !important;
    font-weight: 600;
    color: #1890ff;
  }
}

/* 4. 改进的操作区域 */
@media (max-width: 768px) {
  .user-actions {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: white !important;
    border-top: 1px solid #f0f0f0 !important;
    padding: 12px 16px !important;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.1) !important;
    z-index: 1000 !important;
  }
  
  .chip-selection {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 8px !important;
    margin-bottom: 12px !important;
  }
  
  .chip-selection .ant-btn {
    flex: 1 !important;
    min-width: 60px !important;
    height: 40px !important;
  }
  
  .main-actions {
    display: flex !important;
    gap: 12px !important;
  }
  
  .main-actions .ant-btn {
    flex: 1 !important;
    height: 50px !important;
  }
}

/* 5. 横屏优化 */
@media (max-width: 768px) and (orientation: landscape) {
  .user-actions {
    position: fixed !important;
    right: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    left: auto !important;
    width: 200px !important;
    padding: 8px !important;
    overflow-y: auto !important;
  }
  
  .game-main-area {
    margin-right: 200px !important;
  }
  
  .poker-card {
    min-width: 45px !important;
    min-height: 63px !important;
    font-size: 11px !important;
  }
}

/* 6. 改进的房间信息栏 */
@media (max-width: 768px) {
  .roominfo {
    flex-wrap: wrap !important;
    gap: 8px !important;
    padding: 8px 12px !important;
    background: #fafafa !important;
    border-bottom: 1px solid #f0f0f0 !important;
  }
  
  .roominfo .ant-input {
    min-width: 100px !important;
    height: 36px !important;
  }
  
  .roominfo .ant-btn {
    height: 36px !important;
    padding: 0 12px !important;
  }
}

/* 7. 改进的游戏区域布局 */
@media (max-width: 768px) {
  .game-area {
    padding-bottom: 180px !important; /* 为底部操作区域留空间 */
  }
  
  .boards {
    margin: 16px auto !important;
    justify-content: center !important;
  }
  
  .pots {
    font-size: 16px !important;
    font-weight: 600 !important;
    padding: 8px 16px !important;
    background: #f6ffed !important;
    border: 1px solid #b7eb8f !important;
    border-radius: 20px !important;
    margin: 8px 0 !important;
  }
}

/* 8. 加载和过渡动画 */
.mobile-fade-enter {
  opacity: 0;
  transform: translateY(20px);
}

.mobile-fade-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.mobile-fade-exit {
  opacity: 1;
  transform: translateY(0);
}

.mobile-fade-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}

/* 9. 改进的消息提示 */
@media (max-width: 768px) {
  .ant-message {
    top: 60px !important; /* 避免被状态栏遮挡 */
  }
  
  .ant-message-notice-content {
    font-size: 16px !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
  }
}

/* 10. 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  .poker-card {
    background: #1f1f1f !important;
    color: white !important;
    border-color: #434343 !important;
  }
  
  .user-actions {
    background: #1f1f1f !important;
    border-top-color: #434343 !important;
  }
  
  .roominfo {
    background: #1f1f1f !important;
    border-bottom-color: #434343 !important;
  }
}
