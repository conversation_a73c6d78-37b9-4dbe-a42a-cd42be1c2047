const { randomHands, rank, pokerTypeName } = require('./dist/src/server/utils/game-engine.js');

// 模拟发牌顺序偏差测试
function testDealOrderBias(rounds = 5000, players = 6) {
  const playerStats = Array(players).fill(0).map(() => ({}));
  
  for (let round = 0; round < rounds; round++) {
    const deck = randomHands(52);
    
    // 模拟按固定顺序发牌（就像原代码那样）
    for (let p = 0; p < players; p++) {
      const playerCards = [
        deck[p * 2],         // 第一张手牌
        deck[p * 2 + 1],     // 第二张手牌
        deck[players * 2],   // Flop 1
        deck[players * 2 + 1], // Flop 2  
        deck[players * 2 + 2], // Flop 3
        deck[players * 2 + 4], // Turn (跳过烧牌)
        deck[players * 2 + 6], // River (跳过烧牌)
      ];
      
      const result = rank(playerCards);
      const typeName = pokerTypeName(result.type);
      playerStats[p][typeName] = (playerStats[p][typeName] || 0) + 1;
    }
  }
  
  console.log(`发牌顺序偏差测试 (${rounds}局, ${players}人):`);
  console.log('位置\t一对%\t两对%\t三条%\t同花%\t顺子%\t葫芦%');
  
  for (let p = 0; p < players; p++) {
    const stats = playerStats[p];
    const pairRate = ((stats['一对'] || 0) / rounds * 100).toFixed(1);
    const twoPairRate = ((stats['两对'] || 0) / rounds * 100).toFixed(1);
    const threeRate = ((stats['三条'] || 0) / rounds * 100).toFixed(1);
    const flushRate = ((stats['同花'] || 0) / rounds * 100).toFixed(1);
    const straightRate = ((stats['顺子'] || 0) / rounds * 100).toFixed(1);
    const fullHouseRate = ((stats['葫芦'] || 0) / rounds * 100).toFixed(1);
    
    console.log(`${p+1}\t${pairRate}\t${twoPairRate}\t${threeRate}\t${flushRate}\t${straightRate}\t${fullHouseRate}`);
  }
  
  // 计算方差来检测偏差
  const pairRates = [];
  for (let p = 0; p < players; p++) {
    pairRates.push((playerStats[p]['一对'] || 0) / rounds * 100);
  }
  
  const avgPairRate = pairRates.reduce((a, b) => a + b) / players;
  const variance = pairRates.reduce((sum, rate) => sum + Math.pow(rate - avgPairRate, 2), 0) / players;
  
  console.log(`\n一对概率方差: ${variance.toFixed(3)} (越小越公平，理想值接近0)`);
  if (variance > 2) {
    console.log('⚠️  检测到明显的位置偏差！');
  } else {
    console.log('✅ 位置偏差在可接受范围内');
  }
}

testDealOrderBias();