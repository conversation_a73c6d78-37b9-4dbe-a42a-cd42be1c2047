// 现代化UI设计改进

import React, { useState, useEffect } from 'react';
import { Card, Button, Avatar, Progress, Badge, Tooltip, Space, Typography, Theme } from 'antd';
import { CrownOutlined, FireOutlined, TrophyOutlined, StarOutlined } from '@ant-design/icons';
import styled, { keyframes, ThemeProvider } from 'styled-components';

const { Text, Title } = Typography;

// 1. 现代化主题配置
export const modernTheme = {
  colors: {
    primary: '#6366f1',
    secondary: '#8b5cf6',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    background: '#0f172a',
    surface: '#1e293b',
    surfaceLight: '#334155',
    text: '#f8fafc',
    textSecondary: '#cbd5e1',
    accent: '#06b6d4',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    cardGradient: 'linear-gradient(145deg, #1e293b 0%, #334155 100%)',
    goldGradient: 'linear-gradient(135deg, #ffd700 0%, #ffb347 100%)',
  },
  shadows: {
    small: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
    medium: '0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06)',
    large: '0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)',
    glow: '0 0 20px rgba(99, 102, 241, 0.3)',
  },
  borderRadius: {
    small: '6px',
    medium: '12px',
    large: '20px',
    full: '50%',
  }
};

// 2. 动画效果
const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
`;

const pulse = keyframes`
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
`;

const shimmer = keyframes`
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
`;

const glow = keyframes`
  0%, 100% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.3); }
  50% { box-shadow: 0 0 30px rgba(99, 102, 241, 0.6); }
`;

// 3. 现代化扑克牌组件
const ModernPokerCard = styled.div<{ suit: string; isHighlighted?: boolean }>`
  width: 80px;
  height: 112px;
  background: ${props => props.isHighlighted ? 
    'linear-gradient(145deg, #ffffff 0%, #f0f9ff 100%)' : 
    'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)'};
  border-radius: ${props => props.theme.borderRadius.medium};
  border: 2px solid ${props => props.isHighlighted ? '#3b82f6' : '#e2e8f0'};
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 8px;
  position: relative;
  transition: all 0.3s ease;
  animation: ${fadeIn} 0.5s ease-out;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: ${props => props.theme.shadows.large};
  }
  
  ${props => props.isHighlighted && `
    animation: ${glow} 2s infinite;
  `}
  
  .card-number {
    font-size: 16px;
    font-weight: bold;
    color: ${props => ['h', 'd'].includes(props.suit) ? '#dc2626' : '#1f2937'};
  }
  
  .card-suit {
    font-size: 24px;
    text-align: center;
    color: ${props => ['h', 'd'].includes(props.suit) ? '#dc2626' : '#1f2937'};
  }
  
  .card-corner {
    position: absolute;
    bottom: 8px;
    right: 8px;
    transform: rotate(180deg);
    font-size: 12px;
    font-weight: bold;
    color: ${props => ['h', 'd'].includes(props.suit) ? '#dc2626' : '#1f2937'};
  }
`;

// 4. 现代化用户卡片
const ModernUserCard = styled(Card)<{ isActive?: boolean; isDealer?: boolean }>`
  background: ${props => props.isDealer ? 
    props.theme.colors.goldGradient : 
    props.theme.colors.cardGradient};
  border: 2px solid ${props => props.isActive ? props.theme.colors.primary : 'transparent'};
  border-radius: ${props => props.theme.borderRadius.large};
  transition: all 0.3s ease;
  animation: ${fadeIn} 0.6s ease-out;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.large};
  }
  
  ${props => props.isActive && `
    box-shadow: ${props.theme.shadows.glow};
    animation: ${pulse} 2s infinite;
  `}
  
  .ant-card-body {
    padding: 16px;
    text-align: center;
  }
  
  .user-avatar {
    margin-bottom: 8px;
    position: relative;
  }
  
  .user-name {
    color: ${props => props.theme.colors.text};
    font-weight: 600;
    margin-bottom: 4px;
  }
  
  .user-chips {
    color: ${props => props.theme.colors.success};
    font-weight: bold;
    font-size: 14px;
  }
  
  .user-status {
    position: absolute;
    top: -5px;
    right: -5px;
  }
`;

// 5. 现代化操作按钮
const ModernActionButton = styled(Button)<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  height: 48px;
  border-radius: ${props => props.theme.borderRadius.medium};
  font-weight: 600;
  font-size: 16px;
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  
  ${props => {
    switch (props.variant) {
      case 'primary':
        return `
          background: ${props.theme.colors.gradient};
          color: white;
          &:hover {
            transform: translateY(-2px);
            box-shadow: ${props.theme.shadows.large};
          }
        `;
      case 'danger':
        return `
          background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
          color: white;
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px rgba(239, 68, 68, 0.3);
          }
        `;
      default:
        return `
          background: ${props.theme.colors.surfaceLight};
          color: ${props.theme.colors.text};
          border: 1px solid ${props.theme.colors.surfaceLight};
          &:hover {
            background: ${props.theme.colors.surface};
            transform: translateY(-1px);
          }
        `;
    }
  }}
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }
  
  &:hover::before {
    left: 100%;
  }
`;

// 6. 现代化游戏区域
const ModernGameArea = styled.div`
  background: ${props => props.theme.colors.background};
  min-height: 100vh;
  padding: 20px;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(99, 102, 241, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }
`;

const GameTable = styled.div`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.large};
  padding: 30px;
  margin: 0 auto;
  max-width: 1200px;
  box-shadow: ${props => props.theme.shadows.large};
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: ${props => props.theme.colors.gradient};
    border-radius: ${props => props.theme.borderRadius.large};
    z-index: -1;
  }
`;

// 7. 现代化筹码组件
const ModernChip = styled.div<{ value: number; isSelected?: boolean }>`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  background: ${props => {
    if (props.value >= 1000) return 'linear-gradient(135deg, #1f2937 0%, #374151 100%)';
    if (props.value >= 500) return 'linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%)';
    if (props.value >= 100) return 'linear-gradient(135deg, #059669 0%, #047857 100%)';
    if (props.value >= 25) return 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)';
    return 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)';
  }};
  
  border: 3px solid ${props => props.isSelected ? '#fbbf24' : 'rgba(255,255,255,0.2)'};
  box-shadow: ${props => props.isSelected ? 
    '0 0 20px rgba(251, 191, 36, 0.5)' : 
    props.theme.shadows.medium};
  
  &:hover {
    transform: scale(1.1);
    box-shadow: ${props => props.theme.shadows.large};
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    border-radius: 50%;
    border: 1px dashed rgba(255,255,255,0.3);
  }
`;

// 8. 现代化进度条
const ModernProgressBar = styled.div<{ progress: number }>`
  width: 100%;
  height: 8px;
  background: ${props => props.theme.colors.surfaceLight};
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: ${props => props.progress}%;
    background: ${props => props.theme.colors.gradient};
    border-radius: 4px;
    transition: width 0.3s ease;
  }
`;

// 9. 现代化通知组件
const ModernNotification = styled.div<{ type: 'success' | 'warning' | 'error' | 'info' }>`
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 16px 20px;
  border-radius: ${props => props.theme.borderRadius.medium};
  color: white;
  font-weight: 500;
  z-index: 1000;
  animation: ${fadeIn} 0.3s ease-out;
  
  background: ${props => {
    switch (props.type) {
      case 'success': return props.theme.colors.success;
      case 'warning': return props.theme.colors.warning;
      case 'error': return props.theme.colors.error;
      default: return props.theme.colors.primary;
    }
  }};
  
  box-shadow: ${props => props.theme.shadows.large};
`;

// 10. 现代化统计面板
const StatsPanel = styled.div`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.large};
  padding: 24px;
  margin: 20px 0;
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }
  
  .stat-item {
    text-align: center;
    padding: 16px;
    background: ${props => props.theme.colors.surfaceLight};
    border-radius: ${props => props.theme.borderRadius.medium};
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
    
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: ${props => props.theme.colors.primary};
      margin-bottom: 4px;
    }
    
    .stat-label {
      color: ${props => props.theme.colors.textSecondary};
      font-size: 14px;
    }
  }
`;

// 11. 使用示例组件
export const ModernPokerInterface: React.FC = () => {
  const [selectedChip, setSelectedChip] = useState(25);
  const [gameProgress, setGameProgress] = useState(65);

  return (
    <ThemeProvider theme={modernTheme}>
      <ModernGameArea>
        <GameTable>
          <div style={{ display: 'flex', justifyContent: 'center', gap: '16px', marginBottom: '30px' }}>
            <ModernPokerCard suit="h" isHighlighted>
              <div className="card-number">A</div>
              <div className="card-suit">♥</div>
              <div className="card-corner">A</div>
            </ModernPokerCard>
            <ModernPokerCard suit="s">
              <div className="card-number">K</div>
              <div className="card-suit">♠</div>
              <div className="card-corner">K</div>
            </ModernPokerCard>
          </div>
          
          <div style={{ display: 'flex', justifyContent: 'center', gap: '12px', marginBottom: '30px' }}>
            {[5, 25, 100, 500, 1000].map(value => (
              <ModernChip
                key={value}
                value={value}
                isSelected={selectedChip === value}
                onClick={() => setSelectedChip(value)}
              >
                {value}
              </ModernChip>
            ))}
          </div>
          
          <div style={{ marginBottom: '20px' }}>
            <Text style={{ color: modernTheme.colors.textSecondary, marginBottom: '8px', display: 'block' }}>
              游戏进度
            </Text>
            <ModernProgressBar progress={gameProgress} />
          </div>
          
          <div style={{ display: 'flex', gap: '12px', justifyContent: 'center' }}>
            <ModernActionButton variant="secondary">
              过牌
            </ModernActionButton>
            <ModernActionButton variant="primary">
              跟注
            </ModernActionButton>
            <ModernActionButton variant="danger">
              弃牌
            </ModernActionButton>
          </div>
        </GameTable>
        
        <StatsPanel>
          <Title level={4} style={{ color: modernTheme.colors.text, marginBottom: '20px' }}>
            游戏统计
          </Title>
          <div className="stats-grid">
            <div className="stat-item">
              <div className="stat-value">156</div>
              <div className="stat-label">总手数</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">68%</div>
              <div className="stat-label">胜率</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">+2,450</div>
              <div className="stat-label">总盈利</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">1,200</div>
              <div className="stat-label">最大单锅</div>
            </div>
          </div>
        </StatsPanel>
      </ModernGameArea>
    </ThemeProvider>
  );
};

export {
  ModernPokerCard,
  ModernUserCard,
  ModernActionButton,
  ModernGameArea,
  ModernChip,
  ModernProgressBar,
  ModernNotification,
  StatsPanel,
  modernTheme
};
