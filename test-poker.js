const { randomHands, rank, pokerTypeName } = require('./dist/src/server/utils/game-engine.js');

// 测试成牌率
function testWinRate(rounds = 100000) {
  const stats = {};
  
  for (let i = 0; i < rounds; i++) {
    const cards = randomHands(7);
    const result = rank(cards);
    const typeName = pokerTypeName(result.type);
    stats[typeName] = (stats[typeName] || 0) + 1;
  }
  
  console.log('成牌统计 (100000局):');
  Object.entries(stats)
    .sort((a, b) => b[1] - a[1])
    .forEach(([type, count]) => {
      console.log(`${type}: ${count} (${(count/rounds*100).toFixed(2)}%)`);
    });
}

testWinRate();