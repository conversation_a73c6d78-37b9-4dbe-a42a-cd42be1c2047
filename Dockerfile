# 构建阶段
FROM node:18-alpine as builder

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json以利用Docker缓存
COPY package*.json ./
COPY pnpm-lock.yaml ./

# 安装依赖
RUN npm ci

# 复制源代码
COPY . .

# 创建类型声明文件
RUN mkdir -p src
RUN echo 'declare module "@testing-library/react";' > src/types.d.ts

# 构建前端和后端
RUN npm run build

# 生产阶段
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 设置生产环境
ENV NODE_ENV=production

# 从构建阶段复制必要的文件
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/build ./build
COPY --from=builder /app/node_modules ./node_modules

# 仅安装生产依赖
RUN npm ci --only=production

# 暴露应用端口
EXPOSE 8086

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
  CMD wget --quiet --tries=1 --spider http://localhost:8086 || exit 1

# 启动应用
CMD ["node", "dist/src/server/app-server.js"] 